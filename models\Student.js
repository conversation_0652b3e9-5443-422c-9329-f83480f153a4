const dbConnection = require('../database/connection');
const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

class Student {
    constructor(data) {
        this.student_id = data.student_id;
        this.lrn = data.lrn;
        this.first_name = data.first_name;
        this.last_name = data.last_name;
        this.grade_level = data.grade_level;
        this.section = data.section;
        this.parent_contact = data.parent_contact;
        this.enrollment_status = data.enrollment_status;
        this.photo_path = data.photo_path;
        this.qr_code_data = data.qr_code_data;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
    }

    /**
     * Create a new student
     * @param {Object} studentData - Student data
     * @returns {Promise<Student>}
     */
    static async create(studentData) {
        const { lrn, first_name, last_name, grade_level, section, parent_contact, photo_path, enrollment_status = 'active' } = studentData;

        // Check if LRN already exists
        const existingStudent = await Student.findByLRN(lrn);
        if (existingStudent) {
            throw new Error(`Student with LRN ${lrn} already exists`);
        }

        // Generate QR code data with more comprehensive information
        const qr_code_data = JSON.stringify({
            student_id: null, // Will be updated after insertion
            lrn: lrn,
            grade_level: grade_level,
            section: section,
            type: 'STUDENT'
        });

        const result = await dbConnection.run(
            `INSERT INTO students (lrn, first_name, last_name, grade_level, section, parent_contact, photo_path, qr_code_data, enrollment_status)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [lrn, first_name, last_name, grade_level, section, parent_contact, photo_path, qr_code_data, enrollment_status]
        );

        // Update QR code data with actual student_id
        const finalQrData = JSON.stringify({
            student_id: result.lastID,
            lrn: lrn,
            grade_level: grade_level,
            section: section,
            type: 'STUDENT'
        });

        await dbConnection.run(
            'UPDATE students SET qr_code_data = ? WHERE student_id = ?',
            [finalQrData, result.lastID]
        );

        return await Student.findById(result.lastID);
    }

    /**
     * Find student by ID
     * @param {number} id - Student ID
     * @returns {Promise<Student|null>}
     */
    static async findById(id) {
        const row = await dbConnection.get(
            'SELECT * FROM students WHERE student_id = ?',
            [id]
        );
        
        return row ? new Student(row) : null;
    }

    /**
     * Find student by LRN
     * @param {string} lrn - Learner Reference Number
     * @returns {Promise<Student|null>}
     */
    static async findByLRN(lrn) {
        const row = await dbConnection.get(
            'SELECT * FROM students WHERE lrn = ?',
            [lrn]
        );
        
        return row ? new Student(row) : null;
    }

    /**
     * Find student by QR code data
     * @param {string} qrCodeData - QR code data
     * @returns {Promise<Student|null>}
     */
    static async findByQRCode(qrCodeData) {
        const row = await dbConnection.get(
            'SELECT * FROM students WHERE qr_code_data = ?',
            [qrCodeData]
        );
        
        return row ? new Student(row) : null;
    }

    /**
     * Get all students with advanced filtering and pagination
     * @param {Object} options - Query options
     * @returns {Promise<{students: Student[], total: number, page: number, totalPages: number}>}
     */
    static async findAll(options = {}) {
        const {
            filters = {},
            search = '',
            page = 1,
            limit = 20,
            sortBy = 'last_name',
            sortOrder = 'ASC'
        } = options;

        let sql = 'SELECT * FROM students WHERE 1=1';
        let countSql = 'SELECT COUNT(*) as total FROM students WHERE 1=1';
        let params = [];
        let countParams = [];

        // Apply filters
        if (filters.grade_level) {
            sql += ' AND grade_level = ?';
            countSql += ' AND grade_level = ?';
            params.push(filters.grade_level);
            countParams.push(filters.grade_level);
        }

        if (filters.section) {
            sql += ' AND section = ?';
            countSql += ' AND section = ?';
            params.push(filters.section);
            countParams.push(filters.section);
        }

        if (filters.enrollment_status) {
            sql += ' AND enrollment_status = ?';
            countSql += ' AND enrollment_status = ?';
            params.push(filters.enrollment_status);
            countParams.push(filters.enrollment_status);
        }

        // Apply search
        if (search) {
            const searchCondition = ' AND (first_name LIKE ? OR last_name LIKE ? OR lrn LIKE ? OR parent_contact LIKE ?)';
            const searchParam = `%${search}%`;
            sql += searchCondition;
            countSql += searchCondition;
            params.push(searchParam, searchParam, searchParam, searchParam);
            countParams.push(searchParam, searchParam, searchParam, searchParam);
        }

        // Get total count
        const countResult = await dbConnection.get(countSql, countParams);
        const total = countResult.total;

        // Apply sorting
        const validSortColumns = ['first_name', 'last_name', 'lrn', 'grade_level', 'section', 'enrollment_status', 'created_at'];
        const validSortOrder = ['ASC', 'DESC'];

        if (validSortColumns.includes(sortBy) && validSortOrder.includes(sortOrder.toUpperCase())) {
            sql += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
        } else {
            sql += ' ORDER BY last_name ASC, first_name ASC';
        }

        // Apply pagination
        const offset = (page - 1) * limit;
        sql += ' LIMIT ? OFFSET ?';
        params.push(limit, offset);

        const rows = await dbConnection.all(sql, params);
        const students = rows.map(row => new Student(row));

        const totalPages = Math.ceil(total / limit);

        return {
            students,
            total,
            page: parseInt(page),
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
        };
    }

    /**
     * Update student
     * @param {Object} updateData - Data to update
     * @returns {Promise<Student>}
     */
    async update(updateData) {
        const fields = [];
        const values = [];
        
        for (const [key, value] of Object.entries(updateData)) {
            if (key !== 'student_id' && key !== 'created_at') {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }
        
        if (fields.length === 0) {
            throw new Error('No valid fields to update');
        }
        
        values.push(this.student_id);
        
        await dbConnection.run(
            `UPDATE students SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE student_id = ?`,
            values
        );
        
        return await Student.findById(this.student_id);
    }

    /**
     * Delete student
     * @returns {Promise<boolean>}
     */
    async delete() {
        const result = await dbConnection.run(
            'DELETE FROM students WHERE student_id = ?',
            [this.student_id]
        );
        
        return result.changes > 0;
    }

    /**
     * Generate QR code image
     * @returns {Promise<string>} Base64 encoded QR code image
     */
    async generateQRCode() {
        try {
            return await QRCode.toDataURL(this.qr_code_data);
        } catch (error) {
            throw new Error('Failed to generate QR code: ' + error.message);
        }
    }

    /**
     * Get full name
     * @returns {string}
     */
    getFullName() {
        return `${this.first_name} ${this.last_name}`;
    }

    /**
     * Convert to JSON
     * @returns {Object}
     */
    toJSON() {
        return {
            student_id: this.student_id,
            lrn: this.lrn,
            first_name: this.first_name,
            last_name: this.last_name,
            full_name: this.getFullName(),
            grade_level: this.grade_level,
            section: this.section,
            parent_contact: this.parent_contact,
            enrollment_status: this.enrollment_status,
            photo_path: this.photo_path,
            qr_code_data: this.qr_code_data,
            created_at: this.created_at,
            updated_at: this.updated_at
        };
    }

    /**
     * Bulk update students enrollment status
     * @param {number[]} studentIds - Array of student IDs
     * @param {string} status - New enrollment status
     * @returns {Promise<number>} Number of updated records
     */
    static async bulkUpdateStatus(studentIds, status) {
        if (!studentIds || studentIds.length === 0) {
            return 0;
        }

        const validStatuses = ['active', 'inactive', 'transferred', 'graduated'];
        if (!validStatuses.includes(status)) {
            throw new Error('Invalid enrollment status');
        }

        const placeholders = studentIds.map(() => '?').join(',');
        const result = await dbConnection.run(
            `UPDATE students SET enrollment_status = ?, updated_at = CURRENT_TIMESTAMP
             WHERE student_id IN (${placeholders})`,
            [status, ...studentIds]
        );

        return result.changes;
    }

    /**
     * Get students by grade and section
     * @param {string} grade_level - Grade level
     * @param {string} section - Section
     * @returns {Promise<Student[]>}
     */
    static async findByGradeAndSection(grade_level, section) {
        const rows = await dbConnection.all(
            'SELECT * FROM students WHERE grade_level = ? AND section = ? ORDER BY last_name, first_name',
            [grade_level, section]
        );

        return rows.map(row => new Student(row));
    }

    /**
     * Get unique grade levels
     * @returns {Promise<string[]>}
     */
    static async getGradeLevels() {
        const rows = await dbConnection.all(
            'SELECT DISTINCT grade_level FROM students ORDER BY grade_level'
        );

        return rows.map(row => row.grade_level);
    }

    /**
     * Get unique sections for a grade level
     * @param {string} grade_level - Grade level
     * @returns {Promise<string[]>}
     */
    static async getSectionsByGrade(grade_level) {
        const rows = await dbConnection.all(
            'SELECT DISTINCT section FROM students WHERE grade_level = ? ORDER BY section',
            [grade_level]
        );

        return rows.map(row => row.section);
    }

    /**
     * Get student statistics
     * @returns {Promise<Object>}
     */
    static async getStatistics() {
        const totalResult = await dbConnection.get('SELECT COUNT(*) as total FROM students');
        const activeResult = await dbConnection.get('SELECT COUNT(*) as active FROM students WHERE enrollment_status = "active"');
        const inactiveResult = await dbConnection.get('SELECT COUNT(*) as inactive FROM students WHERE enrollment_status = "inactive"');

        const gradeStats = await dbConnection.all(`
            SELECT grade_level, COUNT(*) as count
            FROM students
            WHERE enrollment_status = 'active'
            GROUP BY grade_level
            ORDER BY grade_level
        `);

        return {
            total: totalResult.total,
            active: activeResult.active,
            inactive: inactiveResult.inactive,
            byGrade: gradeStats
        };
    }

    /**
     * Import students from CSV file
     * @param {string} filePath - Path to CSV file
     * @param {Object} options - Import options
     * @returns {Promise<Object>} Import results
     */
    static async importFromCSV(filePath, options = {}) {
        const { skipDuplicates = true, updateExisting = false } = options;

        return new Promise((resolve, reject) => {
            const results = {
                total: 0,
                success: 0,
                errors: [],
                duplicates: 0,
                updated: 0
            };

            const students = [];

            fs.createReadStream(filePath)
                .pipe(csv())
                .on('data', (data) => {
                    results.total++;

                    // Validate required fields
                    const requiredFields = ['lrn', 'first_name', 'last_name', 'grade_level', 'section', 'parent_contact'];
                    const missingFields = requiredFields.filter(field => !data[field] || data[field].trim() === '');

                    if (missingFields.length > 0) {
                        results.errors.push({
                            row: results.total,
                            lrn: data.lrn || 'N/A',
                            error: `Missing required fields: ${missingFields.join(', ')}`
                        });
                        return;
                    }

                    // Validate LRN format (should be numeric and appropriate length)
                    if (!/^\d{12}$/.test(data.lrn.trim())) {
                        results.errors.push({
                            row: results.total,
                            lrn: data.lrn,
                            error: 'LRN must be exactly 12 digits'
                        });
                        return;
                    }

                    // Validate enrollment status
                    const validStatuses = ['active', 'inactive', 'transferred', 'graduated'];
                    const enrollmentStatus = data.enrollment_status ? data.enrollment_status.trim().toLowerCase() : 'active';
                    if (!validStatuses.includes(enrollmentStatus)) {
                        results.errors.push({
                            row: results.total,
                            lrn: data.lrn,
                            error: `Invalid enrollment status: ${data.enrollment_status}. Must be one of: ${validStatuses.join(', ')}`
                        });
                        return;
                    }

                    students.push({
                        lrn: data.lrn.trim(),
                        first_name: data.first_name.trim(),
                        last_name: data.last_name.trim(),
                        grade_level: data.grade_level.trim(),
                        section: data.section.trim(),
                        parent_contact: data.parent_contact.trim(),
                        enrollment_status: enrollmentStatus
                    });
                })
                .on('end', async () => {
                    try {
                        // Process each student
                        for (const studentData of students) {
                            try {
                                const existingStudent = await Student.findByLRN(studentData.lrn);

                                if (existingStudent) {
                                    if (updateExisting) {
                                        await existingStudent.update(studentData);
                                        results.updated++;
                                    } else if (skipDuplicates) {
                                        results.duplicates++;
                                    } else {
                                        results.errors.push({
                                            lrn: studentData.lrn,
                                            error: 'Student with this LRN already exists'
                                        });
                                    }
                                } else {
                                    await Student.create(studentData);
                                    results.success++;
                                }
                            } catch (error) {
                                results.errors.push({
                                    lrn: studentData.lrn,
                                    error: error.message
                                });
                            }
                        }

                        // Clean up uploaded file
                        if (fs.existsSync(filePath)) {
                            fs.unlinkSync(filePath);
                        }

                        resolve(results);
                    } catch (error) {
                        reject(error);
                    }
                })
                .on('error', (error) => {
                    reject(error);
                });
        });
    }

    /**
     * Generate CSV template
     * @returns {string} CSV template content
     */
    static generateCSVTemplate() {
        const headers = [
            'lrn',
            'first_name',
            'last_name',
            'grade_level',
            'section',
            'parent_contact',
            'enrollment_status'
        ];

        const sampleData = [
            '123456789012,Juan,Dela Cruz,Grade 7,Section A,09123456789,active',
            '123456789013,Maria,Santos,Grade 8,Section B,09123456790,active',
            '123456789014,Jose,Garcia,Grade 9,Section A,09123456791,active'
        ];

        return headers.join(',') + '\n' + sampleData.join('\n');
    }

    /**
     * Validate QR code data
     * @param {string} qrData - QR code data to validate
     * @returns {Object} Validation result
     */
    static validateQRData(qrData) {
        try {
            const payload = JSON.parse(qrData);

            // Check if it's a student QR code
            if (payload.type !== 'STUDENT') {
                return {
                    valid: false,
                    error: 'Not a student QR code'
                };
            }

            // Check required fields
            const requiredFields = ['student_id', 'lrn', 'grade_level', 'section'];
            const missingFields = requiredFields.filter(field => !payload[field]);

            if (missingFields.length > 0) {
                return {
                    valid: false,
                    error: `Missing required fields: ${missingFields.join(', ')}`
                };
            }

            return {
                valid: true,
                payload: payload
            };

        } catch (error) {
            return {
                valid: false,
                error: 'Invalid QR code format'
            };
        }
    }
}

module.exports = Student;
