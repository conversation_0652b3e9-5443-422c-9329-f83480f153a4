const dbConnection = require('./connection');
const bcrypt = require('bcrypt');

/**
 * Initialize database with tables and default data
 */
async function initializeDatabase() {
    try {
        console.log('Starting database initialization...');
        
        // Connect to database
        await dbConnection.connect();
        
        // Initialize tables from schema
        await dbConnection.initializeTables();
        
        // Create default admin user
        await createDefaultAdmin();
        
        // Initialize SMS system
        await initializeSMSSystem();

        // Create sample data (optional)
        await createSampleData();

        console.log('Database initialization completed successfully!');
        
    } catch (error) {
        console.error('Database initialization failed:', error.message);
        process.exit(1);
    } finally {
        await dbConnection.close();
    }
}

/**
 * Create default admin user
 */
async function createDefaultAdmin() {
    try {
        // Check if admin already exists
        const existingAdmin = await dbConnection.get(
            'SELECT user_id FROM users WHERE username = ?',
            ['admin']
        );
        
        if (existingAdmin) {
            console.log('Default admin user already exists.');
            return;
        }
        
        // Create default admin
        const defaultPassword = 'admin123'; // Change this in production!
        const hashedPassword = await bcrypt.hash(defaultPassword, 10);
        
        await dbConnection.run(
            `INSERT INTO users (username, password_hash, role, full_name, contact_number) 
             VALUES (?, ?, ?, ?, ?)`,
            ['admin', hashedPassword, 'admin', 'System Administrator', '09123456789']
        );
        
        console.log('Default admin user created successfully.');
        console.log('Username: admin');
        console.log('Password: admin123');
        console.log('*** PLEASE CHANGE THE DEFAULT PASSWORD IN PRODUCTION! ***');
        
    } catch (error) {
        console.error('Error creating default admin:', error.message);
        throw error;
    }
}

/**
 * Initialize SMS system with default configuration and templates
 */
async function initializeSMSSystem() {
    try {
        console.log('Initializing SMS system...');

        // Check if SMS config already exists
        const existingConfig = await dbConnection.get('SELECT config_id FROM sms_config LIMIT 1');

        if (!existingConfig) {
            // Create default SMS configuration
            await dbConnection.run(
                `INSERT INTO sms_config (textbee_server_url, default_sender_name, max_retry_attempts, retry_delay_minutes, queue_batch_size, enable_sms, enable_delivery_reports)
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                ['http://localhost:8080', 'School', 3, 5, 10, 1, 1]
            );
            console.log('Default SMS configuration created.');
        } else {
            console.log('SMS configuration already exists.');
        }

        // Create default SMS templates
        const templates = [
            {
                name: 'attendance_confirmation',
                type: 'attendance',
                template: 'Good morning! {{student_name}} has arrived at school at {{time}} for {{subject}}. - {{teacher_name}}',
                variables: JSON.stringify(['student_name', 'time', 'subject', 'teacher_name'])
            },
            {
                name: 'absence_notification',
                type: 'absence',
                template: '{{student_name}} is marked absent for {{subject}} at {{time}}. Please verify. - {{school_name}}',
                variables: JSON.stringify(['student_name', 'subject', 'time', 'school_name'])
            },
            {
                name: 'late_arrival',
                type: 'late',
                template: '{{student_name}} arrived late at {{time}} for {{subject}} ({{minutes_late}} minutes late). - {{teacher_name}}',
                variables: JSON.stringify(['student_name', 'time', 'subject', 'minutes_late', 'teacher_name'])
            },
            {
                name: 'general_announcement',
                type: 'announcement',
                template: 'School Announcement: {{message}} - {{school_name}}',
                variables: JSON.stringify(['message', 'school_name'])
            },
            {
                name: 'emergency_alert',
                type: 'emergency',
                template: 'URGENT: {{message}} Please contact the school immediately. - {{school_name}}',
                variables: JSON.stringify(['message', 'school_name'])
            }
        ];

        let templatesCreated = 0;
        for (const template of templates) {
            // Check if template already exists
            const existingTemplate = await dbConnection.get(
                'SELECT template_id FROM sms_templates WHERE template_name = ?',
                [template.name]
            );

            if (!existingTemplate) {
                await dbConnection.run(
                    `INSERT INTO sms_templates (template_name, template_type, message_template, variables, is_active)
                     VALUES (?, ?, ?, ?, ?)`,
                    [template.name, template.type, template.template, template.variables, 1]
                );
                templatesCreated++;
            }
        }

        console.log(`SMS templates initialized. ${templatesCreated} new templates created.`);
        if (templatesCreated === 0) {
            console.log('All SMS templates already exist.');
        }

        console.log('SMS system initialization completed.');

    } catch (error) {
        console.error('Error initializing SMS system:', error.message);
        throw error;
    }
}

/**
 * Create sample data for testing (optional)
 */
async function createSampleData() {
    try {
        console.log('Creating sample data...');

        // Check if sample teacher already exists
        const existingTeacher = await dbConnection.get(
            'SELECT user_id FROM users WHERE username = ?',
            ['teacher1']
        );

        let teacherId;
        if (existingTeacher) {
            console.log('Sample teacher already exists.');
            teacherId = existingTeacher.user_id;
        } else {
            // Create sample teacher
            const teacherPassword = await bcrypt.hash('teacher123', 10);
            const teacherResult = await dbConnection.run(
                `INSERT INTO users (username, password_hash, role, full_name, contact_number)
                 VALUES (?, ?, ?, ?, ?)`,
                ['teacher1', teacherPassword, 'teacher', 'John Doe', '09987654321']
            );
            teacherId = teacherResult.lastID;
            console.log('Sample teacher created.');
        }
        
        // Check if sample subject already exists
        const existingSubject = await dbConnection.get(
            'SELECT subject_id FROM subjects WHERE subject_code = ?',
            ['MATH101']
        );

        if (!existingSubject) {
            // Create sample subject
            await dbConnection.run(
                `INSERT INTO subjects (subject_code, subject_name, grade_level, teacher_id)
                 VALUES (?, ?, ?, ?)`,
                ['MATH101', 'Mathematics', 'Grade 7', teacherId]
            );
            console.log('Sample subject created.');
        } else {
            console.log('Sample subject already exists.');
        }

        // Create sample students
        const students = [
            {
                lrn: '123456789012',
                firstName: 'Alice',
                lastName: 'Johnson',
                gradeLevel: 'Grade 7',
                section: 'Section A',
                parentContact: '09111111111',
                qrCode: 'STUDENT_123456789012'
            },
            {
                lrn: '123456789013',
                firstName: 'Bob',
                lastName: 'Smith',
                gradeLevel: 'Grade 7',
                section: 'Section A',
                parentContact: '09222222222',
                qrCode: 'STUDENT_123456789013'
            }
        ];

        let studentsCreated = 0;
        for (const student of students) {
            // Check if student already exists
            const existingStudent = await dbConnection.get(
                'SELECT student_id FROM students WHERE lrn = ?',
                [student.lrn]
            );

            if (!existingStudent) {
                await dbConnection.run(
                    `INSERT INTO students (lrn, first_name, last_name, grade_level, section, parent_contact, qr_code_data)
                     VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [student.lrn, student.firstName, student.lastName, student.gradeLevel,
                     student.section, student.parentContact, student.qrCode]
                );
                studentsCreated++;
            }
        }

        console.log(`Sample data created successfully. ${studentsCreated} new students added.`);
        if (studentsCreated === 0) {
            console.log('All sample students already exist.');
        }
        console.log('Sample teacher - Username: teacher1, Password: teacher123');
        
    } catch (error) {
        console.error('Error creating sample data:', error.message);
        // Don't throw error for sample data - it's optional
    }
}

// Run initialization if this file is executed directly
if (require.main === module) {
    initializeDatabase();
}

module.exports = {
    initializeDatabase,
    createDefaultAdmin,
    initializeSMSSystem,
    createSampleData
};
