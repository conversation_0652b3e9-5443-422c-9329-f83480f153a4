const dbConnection = require('../database/connection');
const moment = require('moment');

/**
 * SMS Message Template System
 * Handles dynamic message templates with variable substitution and personalization
 */
class SMSTemplateService {
    constructor() {
        this.templateCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = null;
    }

    /**
     * Get template by name with caching
     * @param {string} templateName - Template name
     * @returns {Promise<Object|null>} - Template object or null
     */
    async getTemplate(templateName) {
        try {
            // Check cache first
            if (this.isValidCache() && this.templateCache.has(templateName)) {
                return this.templateCache.get(templateName);
            }

            // Load from database
            const template = await dbConnection.get(
                'SELECT * FROM sms_templates WHERE template_name = ? AND is_active = 1',
                [templateName]
            );

            if (template) {
                // Parse variables JSON
                template.variables = template.variables ? JSON.parse(template.variables) : [];
                
                // Cache the template
                this.templateCache.set(templateName, template);
                this.lastCacheUpdate = Date.now();
            }

            return template;

        } catch (error) {
            console.error('Error getting template:', error.message);
            return null;
        }
    }

    /**
     * Get all templates by type
     * @param {string} templateType - Template type (attendance, absence, late, etc.)
     * @returns {Promise<Array>} - Array of templates
     */
    async getTemplatesByType(templateType) {
        try {
            const templates = await dbConnection.all(
                'SELECT * FROM sms_templates WHERE template_type = ? AND is_active = 1 ORDER BY template_name',
                [templateType]
            );

            return templates.map(template => ({
                ...template,
                variables: template.variables ? JSON.parse(template.variables) : []
            }));

        } catch (error) {
            console.error('Error getting templates by type:', error.message);
            return [];
        }
    }

    /**
     * Get all active templates
     * @returns {Promise<Array>} - Array of all active templates
     */
    async getAllTemplates() {
        try {
            const templates = await dbConnection.all(
                'SELECT * FROM sms_templates WHERE is_active = 1 ORDER BY template_type, template_name'
            );

            return templates.map(template => ({
                ...template,
                variables: template.variables ? JSON.parse(template.variables) : []
            }));

        } catch (error) {
            console.error('Error getting all templates:', error.message);
            return [];
        }
    }

    /**
     * Process template with variable substitution
     * @param {string} templateName - Template name
     * @param {Object} variables - Variables to substitute
     * @returns {Promise<Object>} - Processed message result
     */
    async processTemplate(templateName, variables = {}) {
        try {
            const template = await this.getTemplate(templateName);
            if (!template) {
                return {
                    success: false,
                    error: `Template '${templateName}' not found`
                };
            }

            // Process the template with variables
            const processedMessage = this.substituteVariables(template.message_template, variables);

            return {
                success: true,
                message: processedMessage,
                template: template,
                variables: variables
            };

        } catch (error) {
            console.error('Error processing template:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Substitute variables in template string
     * @param {string} template - Template string with {{variable}} placeholders
     * @param {Object} variables - Variables to substitute
     * @returns {string} - Processed message
     */
    substituteVariables(template, variables) {
        let processedMessage = template;

        // Replace all {{variable}} placeholders
        processedMessage = processedMessage.replace(/\{\{(\w+)\}\}/g, (match, variableName) => {
            if (variables.hasOwnProperty(variableName)) {
                return variables[variableName];
            }
            // Return placeholder if variable not found
            return match;
        });

        return processedMessage;
    }

    /**
     * Create attendance confirmation message
     * @param {Object} attendanceData - Attendance data
     * @returns {Promise<Object>} - Message result
     */
    async createAttendanceMessage(attendanceData) {
        try {
            const {
                student_name,
                subject_name,
                teacher_name,
                timestamp,
                status = 'present'
            } = attendanceData;

            const templateName = status === 'late' ? 'late_arrival' : 'attendance_confirmation';
            const time = moment(timestamp).format('h:mm A');

            const variables = {
                student_name: student_name,
                subject: subject_name,
                teacher_name: teacher_name,
                time: time,
                school_name: 'School', // This could be configurable
                minutes_late: attendanceData.minutes_late || 0
            };

            return await this.processTemplate(templateName, variables);

        } catch (error) {
            console.error('Error creating attendance message:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create absence notification message
     * @param {Object} absenceData - Absence data
     * @returns {Promise<Object>} - Message result
     */
    async createAbsenceMessage(absenceData) {
        try {
            const {
                student_name,
                subject_name,
                timestamp
            } = absenceData;

            const time = moment(timestamp).format('h:mm A');

            const variables = {
                student_name: student_name,
                subject: subject_name,
                time: time,
                school_name: 'School' // This could be configurable
            };

            return await this.processTemplate('absence_notification', variables);

        } catch (error) {
            console.error('Error creating absence message:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create announcement message
     * @param {Object} announcementData - Announcement data
     * @returns {Promise<Object>} - Message result
     */
    async createAnnouncementMessage(announcementData) {
        try {
            const {
                message,
                is_emergency = false
            } = announcementData;

            const templateName = is_emergency ? 'emergency_alert' : 'general_announcement';

            const variables = {
                message: message,
                school_name: 'School' // This could be configurable
            };

            return await this.processTemplate(templateName, variables);

        } catch (error) {
            console.error('Error creating announcement message:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create or update template
     * @param {Object} templateData - Template data
     * @returns {Promise<Object>} - Create/update result
     */
    async saveTemplate(templateData) {
        try {
            const {
                template_id = null,
                template_name,
                template_type,
                message_template,
                variables = [],
                is_active = true,
                created_by = null
            } = templateData;

            // Validate required fields
            if (!template_name || !template_type || !message_template) {
                return {
                    success: false,
                    error: 'Missing required fields: template_name, template_type, message_template'
                };
            }

            // Validate template type
            const validTypes = ['attendance', 'absence', 'late', 'announcement', 'emergency'];
            if (!validTypes.includes(template_type)) {
                return {
                    success: false,
                    error: `Invalid template type. Must be one of: ${validTypes.join(', ')}`
                };
            }

            const variablesJson = JSON.stringify(variables);

            if (template_id) {
                // Update existing template
                await dbConnection.run(
                    `UPDATE sms_templates 
                     SET template_name = ?, template_type = ?, message_template = ?, 
                         variables = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                     WHERE template_id = ?`,
                    [template_name, template_type, message_template, variablesJson, is_active, template_id]
                );

                // Clear cache for this template
                this.templateCache.delete(template_name);

                return {
                    success: true,
                    template_id: template_id,
                    message: 'Template updated successfully'
                };

            } else {
                // Create new template
                const result = await dbConnection.run(
                    `INSERT INTO sms_templates (template_name, template_type, message_template, variables, is_active, created_by)
                     VALUES (?, ?, ?, ?, ?, ?)`,
                    [template_name, template_type, message_template, variablesJson, is_active, created_by]
                );

                return {
                    success: true,
                    template_id: result.lastID,
                    message: 'Template created successfully'
                };
            }

        } catch (error) {
            console.error('Error saving template:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete template
     * @param {number} templateId - Template ID
     * @returns {Promise<Object>} - Delete result
     */
    async deleteTemplate(templateId) {
        try {
            // Get template name for cache clearing
            const template = await dbConnection.get(
                'SELECT template_name FROM sms_templates WHERE template_id = ?',
                [templateId]
            );

            const result = await dbConnection.run(
                'DELETE FROM sms_templates WHERE template_id = ?',
                [templateId]
            );

            if (result.changes > 0) {
                // Clear cache
                if (template) {
                    this.templateCache.delete(template.template_name);
                }

                return {
                    success: true,
                    message: 'Template deleted successfully'
                };
            } else {
                return {
                    success: false,
                    error: 'Template not found'
                };
            }

        } catch (error) {
            console.error('Error deleting template:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Validate template variables
     * @param {string} template - Template string
     * @returns {Array} - Array of variable names found in template
     */
    extractTemplateVariables(template) {
        const variableRegex = /\{\{(\w+)\}\}/g;
        const variables = [];
        let match;

        while ((match = variableRegex.exec(template)) !== null) {
            if (!variables.includes(match[1])) {
                variables.push(match[1]);
            }
        }

        return variables;
    }

    /**
     * Preview template with sample data
     * @param {string} template - Template string
     * @param {Object} sampleData - Sample data for preview
     * @returns {Object} - Preview result
     */
    previewTemplate(template, sampleData = {}) {
        try {
            // Extract variables from template
            const variables = this.extractTemplateVariables(template);

            // Create sample data if not provided
            const defaultSampleData = {
                student_name: 'John Doe',
                subject: 'Mathematics',
                teacher_name: 'Ms. Smith',
                time: '8:30 AM',
                school_name: 'Sample School',
                minutes_late: '15',
                message: 'Sample announcement message'
            };

            const previewData = { ...defaultSampleData, ...sampleData };
            const previewMessage = this.substituteVariables(template, previewData);

            return {
                success: true,
                preview: previewMessage,
                variables: variables,
                sampleData: previewData
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Check if template cache is valid
     * @returns {boolean} - Whether cache is valid
     */
    isValidCache() {
        return this.lastCacheUpdate && (Date.now() - this.lastCacheUpdate) < this.cacheExpiry;
    }

    /**
     * Clear template cache
     */
    clearCache() {
        this.templateCache.clear();
        this.lastCacheUpdate = null;
    }

    /**
     * Get template usage statistics
     * @param {number} days - Number of days to look back (default: 30)
     * @returns {Promise<Object>} - Usage statistics
     */
    async getTemplateUsageStats(days = 30) {
        try {
            const cutoffDate = moment().subtract(days, 'days').toISOString();

            const stats = await dbConnection.all(
                `SELECT 
                    t.template_name,
                    t.template_type,
                    COUNT(l.sms_id) as usage_count,
                    COUNT(CASE WHEN l.delivery_status = 'delivered' THEN 1 END) as delivered_count,
                    COUNT(CASE WHEN l.delivery_status = 'failed' THEN 1 END) as failed_count
                 FROM sms_templates t
                 LEFT JOIN sms_logs l ON t.template_id = l.template_id 
                   AND l.created_at >= ?
                 WHERE t.is_active = 1
                 GROUP BY t.template_id, t.template_name, t.template_type
                 ORDER BY usage_count DESC`,
                [cutoffDate]
            );

            return {
                success: true,
                stats: stats,
                period_days: days
            };

        } catch (error) {
            console.error('Error getting template usage stats:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create singleton instance
const smsTemplateService = new SMSTemplateService();

module.exports = smsTemplateService;
