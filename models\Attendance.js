const dbConnection = require('../database/connection');
const ClassSession = require('./ClassSession');
const Student = require('./Student');
const User = require('./User');
const moment = require('moment');
const smsService = require('../services/sms-service');

class Attendance {
    constructor(data) {
        this.attendance_id = data.attendance_id;
        this.student_id = data.student_id;
        this.session_id = data.session_id;
        this.timestamp = data.timestamp;
        this.status = data.status;
        this.teacher_id = data.teacher_id;
        this.remarks = data.remarks;
        this.created_at = data.created_at;
        
        // For joined queries
        this.student_name = data.student_name;
        this.student_lrn = data.student_lrn;
        this.subject_name = data.subject_name;
        this.teacher_name = data.teacher_name;
    }

    /**
     * Record attendance via QR code scan
     * @param {Object} scanData - Scan data
     * @returns {Promise<Object>} Scan result
     */
    static async recordViaScan(scanData) {
        const { studentQRData, sessionQRData, teacherId, clientIP } = scanData;

        try {
            // Validate student QR code
            const studentValidation = Student.validateQRData ? 
                Student.validateQRData(studentQRData) : 
                { valid: true, payload: JSON.parse(studentQRData) };

            if (!studentValidation.valid) {
                return {
                    success: false,
                    error: 'Invalid student QR code',
                    details: studentValidation.error
                };
            }

            // Validate session QR code
            const sessionValidation = ClassSession.validateQRData(sessionQRData);
            if (!sessionValidation.valid) {
                return {
                    success: false,
                    error: 'Invalid session QR code',
                    details: sessionValidation.error
                };
            }

            const studentPayload = studentValidation.payload;
            const sessionPayload = sessionValidation.payload;

            // Get session details
            const session = await ClassSession.findById(sessionPayload.session_id);
            if (!session) {
                return {
                    success: false,
                    error: 'Session not found'
                };
            }

            // Check if session is active
            if (session.status !== 'active') {
                return {
                    success: false,
                    error: 'Session is not active'
                };
            }

            // Check if QR code is expired
            if (session.isQRExpired()) {
                return {
                    success: false,
                    error: 'QR code has expired'
                };
            }

            // Get student details
            const student = await Student.findById(studentPayload.student_id);
            if (!student) {
                return {
                    success: false,
                    error: 'Student not found'
                };
            }

            // Check if student is active
            if (student.enrollment_status !== 'active') {
                return {
                    success: false,
                    error: 'Student is not actively enrolled'
                };
            }

            // Check for duplicate attendance
            const existingAttendance = await Attendance.findByStudentAndSession(
                student.student_id, 
                session.session_id
            );

            if (existingAttendance) {
                return {
                    success: false,
                    error: 'Attendance already recorded for this session',
                    existing_attendance: existingAttendance.toJSON()
                };
            }

            // Determine attendance status based on timing
            const sessionTime = moment(session.date_time);
            const currentTime = moment();
            const minutesLate = currentTime.diff(sessionTime, 'minutes');

            let attendanceStatus = 'present';
            if (minutesLate > 15) {
                attendanceStatus = 'late';
            }

            // Record attendance
            const attendance = await Attendance.create({
                student_id: student.student_id,
                session_id: session.session_id,
                status: attendanceStatus,
                teacher_id: teacherId,
                remarks: minutesLate > 0 ? `${minutesLate} minutes late` : null
            });

            // Get teacher information for SMS
            const teacher = await User.findById(teacherId);
            const teacherName = teacher ? teacher.full_name : 'Teacher';

            // Send SMS notification to parent
            try {
                const smsResult = await smsService.sendAttendanceNotification({
                    student_id: student.student_id,
                    student_name: student.getFullName(),
                    parent_contact: student.parent_contact,
                    subject_name: session.subject_name,
                    teacher_name: teacherName,
                    teacher_id: teacherId,
                    session_id: session.session_id,
                    attendance_id: attendance.attendance_id,
                    status: attendanceStatus,
                    timestamp: new Date(),
                    minutes_late: minutesLate
                });

                console.log(`SMS notification result for student ${student.student_id}:`, smsResult.success ? 'Queued' : smsResult.error);
            } catch (smsError) {
                console.error('Error sending SMS notification:', smsError.message);
                // Don't fail attendance recording if SMS fails
            }

            return {
                success: true,
                message: 'Attendance recorded successfully',
                attendance: attendance.toJSON(),
                student: {
                    name: student.getFullName(),
                    lrn: student.lrn,
                    grade_level: student.grade_level,
                    section: student.section
                },
                session: {
                    subject_name: session.subject_name,
                    room_number: session.room_number,
                    date_time: session.date_time
                },
                status: attendanceStatus,
                minutes_late: minutesLate > 0 ? minutesLate : 0
            };

        } catch (error) {
            console.error('Error recording attendance:', error);
            return {
                success: false,
                error: 'Failed to record attendance',
                details: error.message
            };
        }
    }

    /**
     * Create new attendance record
     * @param {Object} attendanceData - Attendance data
     * @returns {Promise<Attendance>}
     */
    static async create(attendanceData) {
        const { student_id, session_id, status = 'present', teacher_id, remarks } = attendanceData;

        const result = await dbConnection.run(
            `INSERT INTO attendance (student_id, session_id, status, teacher_id, remarks)
             VALUES (?, ?, ?, ?, ?)`,
            [student_id, session_id, status, teacher_id, remarks]
        );

        return await Attendance.findById(result.lastID);
    }

    /**
     * Find attendance by ID
     * @param {number} id - Attendance ID
     * @returns {Promise<Attendance|null>}
     */
    static async findById(id) {
        const row = await dbConnection.get(
            `SELECT a.*, 
                    CONCAT(st.first_name, ' ', st.last_name) as student_name,
                    st.lrn as student_lrn,
                    s.subject_name,
                    u.full_name as teacher_name
             FROM attendance a
             LEFT JOIN students st ON a.student_id = st.student_id
             LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
             LEFT JOIN subjects s ON cs.subject_id = s.subject_id
             LEFT JOIN users u ON a.teacher_id = u.user_id
             WHERE a.attendance_id = ?`,
            [id]
        );

        return row ? new Attendance(row) : null;
    }

    /**
     * Find attendance by student and session
     * @param {number} studentId - Student ID
     * @param {number} sessionId - Session ID
     * @returns {Promise<Attendance|null>}
     */
    static async findByStudentAndSession(studentId, sessionId) {
        const row = await dbConnection.get(
            `SELECT a.*, 
                    CONCAT(st.first_name, ' ', st.last_name) as student_name,
                    st.lrn as student_lrn,
                    s.subject_name,
                    u.full_name as teacher_name
             FROM attendance a
             LEFT JOIN students st ON a.student_id = st.student_id
             LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
             LEFT JOIN subjects s ON cs.subject_id = s.subject_id
             LEFT JOIN users u ON a.teacher_id = u.user_id
             WHERE a.student_id = ? AND a.session_id = ?`,
            [studentId, sessionId]
        );

        return row ? new Attendance(row) : null;
    }

    /**
     * Get all attendance records with filters
     * @param {Object} filters - Filter options
     * @returns {Promise<Attendance[]>}
     */
    static async findAll(filters = {}) {
        let query = `SELECT a.*, 
                            CONCAT(st.first_name, ' ', st.last_name) as student_name,
                            st.lrn as student_lrn,
                            s.subject_name,
                            u.full_name as teacher_name
                     FROM attendance a
                     LEFT JOIN students st ON a.student_id = st.student_id
                     LEFT JOIN class_sessions cs ON a.session_id = cs.session_id
                     LEFT JOIN subjects s ON cs.subject_id = s.subject_id
                     LEFT JOIN users u ON a.teacher_id = u.user_id`;
        
        const params = [];
        const conditions = [];

        if (filters.student_id) {
            conditions.push('a.student_id = ?');
            params.push(filters.student_id);
        }

        if (filters.session_id) {
            conditions.push('a.session_id = ?');
            params.push(filters.session_id);
        }

        if (filters.teacher_id) {
            conditions.push('a.teacher_id = ?');
            params.push(filters.teacher_id);
        }

        if (filters.status) {
            conditions.push('a.status = ?');
            params.push(filters.status);
        }

        if (filters.date_from) {
            conditions.push('DATE(a.timestamp) >= ?');
            params.push(filters.date_from);
        }

        if (filters.date_to) {
            conditions.push('DATE(a.timestamp) <= ?');
            params.push(filters.date_to);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' ORDER BY a.timestamp DESC';

        const rows = await dbConnection.all(query, params);
        return rows.map(row => new Attendance(row));
    }

    /**
     * Get attendance by session
     * @param {number} sessionId - Session ID
     * @returns {Promise<Attendance[]>}
     */
    static async findBySession(sessionId) {
        return await Attendance.findAll({ session_id: sessionId });
    }

    /**
     * Get attendance by student
     * @param {number} studentId - Student ID
     * @returns {Promise<Attendance[]>}
     */
    static async findByStudent(studentId) {
        return await Attendance.findAll({ student_id: studentId });
    }

    /**
     * Get today's attendance
     * @returns {Promise<Attendance[]>}
     */
    static async findToday() {
        const today = moment().format('YYYY-MM-DD');
        return await Attendance.findAll({ 
            date_from: today, 
            date_to: today 
        });
    }

    /**
     * Get attendance statistics for a session
     * @param {number} sessionId - Session ID
     * @returns {Promise<Object>}
     */
    static async getSessionStats(sessionId) {
        const stats = await dbConnection.get(
            `SELECT 
                COUNT(*) as total_attendance,
                COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count
             FROM attendance 
             WHERE session_id = ?`,
            [sessionId]
        );

        return {
            total_attendance: stats.total_attendance || 0,
            present_count: stats.present_count || 0,
            late_count: stats.late_count || 0,
            absent_count: stats.absent_count || 0
        };
    }

    /**
     * Get attendance statistics for a student
     * @param {number} studentId - Student ID
     * @returns {Promise<Object>}
     */
    static async getStudentStats(studentId) {
        const stats = await dbConnection.get(
            `SELECT 
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count,
                COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count
             FROM attendance 
             WHERE student_id = ?`,
            [studentId]
        );

        const total = stats.total_sessions || 0;
        const present = stats.present_count || 0;
        const late = stats.late_count || 0;

        return {
            total_sessions: total,
            present_count: present,
            late_count: late,
            absent_count: stats.absent_count || 0,
            attendance_rate: total > 0 ? ((present + late) / total * 100).toFixed(2) : 0
        };
    }

    /**
     * Update attendance record
     * @param {Object} updateData - Data to update
     * @returns {Promise<Attendance>}
     */
    async update(updateData) {
        const fields = [];
        const values = [];

        for (const [key, value] of Object.entries(updateData)) {
            if (key !== 'attendance_id' && key !== 'created_at' && 
                key !== 'student_name' && key !== 'teacher_name' && key !== 'subject_name') {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (fields.length === 0) {
            throw new Error('No valid fields to update');
        }

        values.push(this.attendance_id);

        await dbConnection.run(
            `UPDATE attendance SET ${fields.join(', ')} WHERE attendance_id = ?`,
            values
        );

        return await Attendance.findById(this.attendance_id);
    }

    /**
     * Delete attendance record
     * @returns {Promise<boolean>}
     */
    async delete() {
        const result = await dbConnection.run(
            'DELETE FROM attendance WHERE attendance_id = ?',
            [this.attendance_id]
        );

        return result.changes > 0;
    }

    /**
     * Convert to JSON
     * @returns {Object}
     */
    toJSON() {
        return {
            attendance_id: this.attendance_id,
            student_id: this.student_id,
            student_name: this.student_name,
            student_lrn: this.student_lrn,
            session_id: this.session_id,
            subject_name: this.subject_name,
            timestamp: this.timestamp,
            status: this.status,
            teacher_id: this.teacher_id,
            teacher_name: this.teacher_name,
            remarks: this.remarks,
            created_at: this.created_at
        };
    }

    /**
     * Send absence notifications for students who didn't attend a session
     * @param {number} sessionId - Session ID
     * @returns {Promise<Object>}
     */
    static async sendAbsenceNotifications(sessionId) {
        try {
            // Get session details
            const session = await ClassSession.findById(sessionId);
            if (!session) {
                return {
                    success: false,
                    error: 'Session not found'
                };
            }

            // Get teacher information
            const teacher = await User.findById(session.teacher_id);
            const teacherName = teacher ? teacher.full_name : 'Teacher';

            // Get students who were supposed to attend but didn't
            const absentStudents = await dbConnection.all(
                `SELECT DISTINCT s.student_id, s.first_name, s.last_name, s.parent_contact
                 FROM students s
                 INNER JOIN enrollments e ON s.student_id = e.student_id
                 WHERE e.subject_id = ?
                   AND s.enrollment_status = 'active'
                   AND s.student_id NOT IN (
                       SELECT a.student_id
                       FROM attendance a
                       WHERE a.session_id = ?
                   )`,
                [session.subject_id, sessionId]
            );

            if (absentStudents.length === 0) {
                return {
                    success: true,
                    message: 'No absent students found',
                    notificationsSent: 0
                };
            }

            let successCount = 0;
            let failureCount = 0;

            // Send absence notification for each absent student
            for (const student of absentStudents) {
                try {
                    const smsResult = await smsService.sendAbsenceNotification({
                        student_id: student.student_id,
                        student_name: `${student.first_name} ${student.last_name}`,
                        parent_contact: student.parent_contact,
                        subject_name: session.subject_name,
                        teacher_id: session.teacher_id,
                        session_id: sessionId,
                        timestamp: session.date_time
                    });

                    if (smsResult.success) {
                        successCount++;
                        console.log(`Absence notification queued for student ${student.student_id}`);
                    } else {
                        failureCount++;
                        console.error(`Failed to queue absence notification for student ${student.student_id}:`, smsResult.error);
                    }
                } catch (error) {
                    failureCount++;
                    console.error(`Error sending absence notification for student ${student.student_id}:`, error.message);
                }
            }

            return {
                success: true,
                message: `Absence notifications processed for ${absentStudents.length} students`,
                totalStudents: absentStudents.length,
                notificationsSent: successCount,
                notificationsFailed: failureCount
            };

        } catch (error) {
            console.error('Error sending absence notifications:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Mark student as absent and send notification
     * @param {Object} absenceData - Absence data
     * @returns {Promise<Object>}
     */
    static async markAbsent(absenceData) {
        try {
            const { student_id, session_id, teacher_id, remarks = null } = absenceData;

            // Check if attendance already exists
            const existingAttendance = await Attendance.findByStudentAndSession(student_id, session_id);
            if (existingAttendance) {
                return {
                    success: false,
                    error: 'Attendance already recorded for this student and session'
                };
            }

            // Get student and session details
            const student = await Student.findById(student_id);
            const session = await ClassSession.findById(session_id);
            const teacher = await User.findById(teacher_id);

            if (!student || !session) {
                return {
                    success: false,
                    error: 'Student or session not found'
                };
            }

            // Create absence record
            const attendance = await Attendance.create({
                student_id,
                session_id,
                status: 'absent',
                teacher_id,
                remarks
            });

            // Send absence notification
            try {
                const smsResult = await smsService.sendAbsenceNotification({
                    student_id: student.student_id,
                    student_name: student.getFullName(),
                    parent_contact: student.parent_contact,
                    subject_name: session.subject_name,
                    teacher_id: teacher_id,
                    session_id: session_id,
                    timestamp: session.date_time
                });

                console.log(`Absence notification result for student ${student.student_id}:`, smsResult.success ? 'Queued' : smsResult.error);
            } catch (smsError) {
                console.error('Error sending absence SMS notification:', smsError.message);
            }

            return {
                success: true,
                message: 'Student marked as absent and notification sent',
                attendance: attendance.toJSON(),
                student: {
                    name: student.getFullName(),
                    lrn: student.lrn,
                    grade_level: student.grade_level,
                    section: student.section
                },
                session: {
                    subject_name: session.subject_name,
                    room_number: session.room_number,
                    date_time: session.date_time
                }
            };

        } catch (error) {
            console.error('Error marking student as absent:', error.message);
            return {
                success: false,
                error: 'Failed to mark student as absent',
                details: error.message
            };
        }
    }
}

module.exports = Attendance;
