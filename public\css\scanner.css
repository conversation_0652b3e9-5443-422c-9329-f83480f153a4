/* QR Scanner Specific Styles */

.scanner-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

#video {
    width: 100%;
    height: auto;
    display: block;
    background: #000;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.scanner-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    border: 3px solid #28a745;
    border-radius: 15px;
    box-shadow: 0 0 0 9999px rgba(0,0,0,0.4);
    transition: border-color 0.3s ease;
}

.scanner-frame.scanning {
    border-color: #ffc107;
    animation: pulse-border 2s infinite;
}

.scanner-frame.success {
    border-color: #28a745;
    animation: success-flash 0.5s ease;
}

.scanner-frame.error {
    border-color: #dc3545;
    animation: error-shake 0.5s ease;
}

@keyframes pulse-border {
    0%, 100% { border-color: #28a745; }
    50% { border-color: #20c997; }
}

@keyframes success-flash {
    0%, 100% { border-color: #28a745; }
    50% { border-color: #40e0d0; }
}

@keyframes error-shake {
    0%, 100% { transform: translate(-50%, -50%); }
    25% { transform: translate(-48%, -50%); }
    75% { transform: translate(-52%, -50%); }
}

.scanner-frame::before,
.scanner-frame::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 4px solid;
    border-color: inherit;
    transition: border-color 0.3s ease;
}

.scanner-frame::before {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 15px;
}

.scanner-frame::after {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 15px;
}

.scanning-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, #28a745, transparent);
    animation: scan 2s linear infinite;
    border-radius: 2px;
}

@keyframes scan {
    0% { top: 0; opacity: 1; }
    50% { opacity: 0.7; }
    100% { top: calc(100% - 3px); opacity: 1; }
}

.status-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.status-scanning {
    background: rgba(40, 167, 69, 0.9);
    color: white;
    animation: pulse-glow 2s infinite;
}

.status-processing {
    background: rgba(255, 193, 7, 0.9);
    color: #212529;
}

.status-success {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status-error {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 10px rgba(40, 167, 69, 0.5); }
    50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.8); }
}

/* Attendance List Styles */
.attendance-list {
    max-height: 450px;
    overflow-y: auto;
    padding: 10px;
}

.attendance-list::-webkit-scrollbar {
    width: 6px;
}

.attendance-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.attendance-list::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.attendance-list::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.student-card {
    transition: all 0.3s ease;
    border-left: 4px solid #28a745;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.student-card.late {
    border-left-color: #ffc107;
}

.student-card.new-entry {
    animation: slideInRight 0.5s ease;
    border-left-color: #20c997;
}

.student-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.scan-success {
    animation: pulse-success 0.6s ease;
}

@keyframes pulse-success {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Mobile Controls */
.mobile-controls {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.mobile-controls .btn {
    border-radius: 20px;
    padding: 10px 15px;
    margin: 0 5px;
    border: none;
    font-weight: 600;
}

/* Session Info Styles */
.session-info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px;
    margin-bottom: 20px;
}

.session-info-card .alert {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    backdrop-filter: blur(10px);
}

/* Camera Controls */
.camera-controls {
    text-align: center;
    margin: 20px 0;
}

.camera-controls .btn {
    margin: 0 5px;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.camera-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Manual Entry */
.manual-entry {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
}

.manual-entry .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.manual-entry .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
    .scanner-container {
        max-width: 100%;
        margin: 0;
    }
    
    .scanner-frame {
        width: 200px;
        height: 200px;
    }
    
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .status-indicator {
        top: 10px;
        right: 10px;
        padding: 6px 12px;
        font-size: 0.75rem;
    }
    
    .mobile-controls {
        bottom: 10px;
    }
}

@media (max-width: 576px) {
    .scanner-frame {
        width: 180px;
        height: 180px;
    }
    
    .attendance-list {
        max-height: 300px;
    }
    
    .student-card .card-body {
        padding: 10px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .student-card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .manual-entry {
        background: #2d3748;
    }
    
    .manual-entry .form-control {
        background: #4a5568;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}
