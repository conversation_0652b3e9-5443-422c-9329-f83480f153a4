const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const { requireAuth, requireRole } = require('../middleware/auth');
const smsService = require('../services/sms-service');
const smsTemplateService = require('../services/sms-templates');
const dbConnection = require('../database/connection');

/**
 * GET /sms-config - SMS Configuration Dashboard
 */
router.get('/', requireAuth, requireRole(['admin', 'teacher']), async (req, res) => {
    try {
        // Get current SMS configuration
        const config = await dbConnection.get('SELECT * FROM sms_config LIMIT 1');
        
        // Get all templates
        const templates = await smsTemplateService.getAllTemplates();
        
        // Get SMS statistics for the last 30 days
        const stats = await smsService.getStatistics();
        
        // Test connectivity
        const connectivity = await smsService.testConnectivity();

        res.render('sms/config', {
            title: 'SMS Configuration',
            user: req.user,
            config: config,
            templates: templates,
            stats: stats,
            connectivity: connectivity
        });

    } catch (error) {
        console.error('Error loading SMS configuration:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load SMS configuration',
            error: error
        });
    }
});

/**
 * POST /sms-config/settings - Update SMS settings
 */
router.post('/settings', requireAuth, requireRole(['admin']), [
    body('textbee_server_url')
        .notEmpty()
        .isURL()
        .withMessage('Valid TextBee server URL is required'),
    body('textbee_api_key')
        .optional()
        .isLength({ max: 255 })
        .withMessage('API key must be 255 characters or less'),
    body('default_sender_name')
        .notEmpty()
        .isLength({ min: 1, max: 20 })
        .withMessage('Sender name must be between 1 and 20 characters'),
    body('max_retry_attempts')
        .isInt({ min: 1, max: 10 })
        .withMessage('Max retry attempts must be between 1 and 10'),
    body('retry_delay_minutes')
        .isInt({ min: 1, max: 60 })
        .withMessage('Retry delay must be between 1 and 60 minutes'),
    body('queue_batch_size')
        .isInt({ min: 1, max: 100 })
        .withMessage('Queue batch size must be between 1 and 100'),
    body('enable_sms')
        .isBoolean()
        .withMessage('Enable SMS must be boolean'),
    body('enable_delivery_reports')
        .isBoolean()
        .withMessage('Enable delivery reports must be boolean')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const configData = {
            textbee_server_url: req.body.textbee_server_url,
            textbee_api_key: req.body.textbee_api_key || null,
            default_sender_name: req.body.default_sender_name,
            max_retry_attempts: parseInt(req.body.max_retry_attempts),
            retry_delay_minutes: parseInt(req.body.retry_delay_minutes),
            queue_batch_size: parseInt(req.body.queue_batch_size),
            enable_sms: req.body.enable_sms === 'true' || req.body.enable_sms === true,
            enable_delivery_reports: req.body.enable_delivery_reports === 'true' || req.body.enable_delivery_reports === true
        };

        const result = await smsService.updateConfiguration(configData);

        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }

    } catch (error) {
        console.error('Error updating SMS configuration:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update SMS configuration',
            details: error.message
        });
    }
});

/**
 * POST /sms-config/test-connection - Test TextBee connection
 */
router.post('/test-connection', requireAuth, requireRole(['admin', 'teacher']), async (req, res) => {
    try {
        const result = await smsService.testConnectivity();
        
        res.json({
            success: result.success,
            connected: result.textbee_connected,
            status: result.textbee_status,
            server_info: result.server_info,
            queue_stats: result.queue_stats,
            error: result.error
        });

    } catch (error) {
        console.error('Error testing SMS connection:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to test connection',
            details: error.message
        });
    }
});

/**
 * GET /sms-config/templates - Get all SMS templates
 */
router.get('/templates', requireAuth, requireRole(['admin', 'teacher']), async (req, res) => {
    try {
        const { type } = req.query;
        
        let templates;
        if (type) {
            templates = await smsTemplateService.getTemplatesByType(type);
        } else {
            templates = await smsTemplateService.getAllTemplates();
        }

        res.json({
            success: true,
            templates: templates
        });

    } catch (error) {
        console.error('Error getting SMS templates:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get templates',
            details: error.message
        });
    }
});

/**
 * POST /sms-config/templates - Create or update SMS template
 */
router.post('/templates', requireAuth, requireRole(['admin']), [
    body('template_name')
        .notEmpty()
        .isLength({ min: 1, max: 100 })
        .withMessage('Template name must be between 1 and 100 characters'),
    body('template_type')
        .isIn(['attendance', 'absence', 'late', 'announcement', 'emergency'])
        .withMessage('Invalid template type'),
    body('message_template')
        .notEmpty()
        .isLength({ min: 10, max: 500 })
        .withMessage('Message template must be between 10 and 500 characters'),
    body('is_active')
        .optional()
        .isBoolean()
        .withMessage('Active status must be boolean'),
    body('template_id')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Template ID must be a positive integer')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const templateData = {
            template_id: req.body.template_id ? parseInt(req.body.template_id) : null,
            template_name: req.body.template_name,
            template_type: req.body.template_type,
            message_template: req.body.message_template,
            variables: req.body.variables || [],
            is_active: req.body.is_active !== false,
            created_by: req.user.user_id
        };

        const result = await smsTemplateService.saveTemplate(templateData);

        if (result.success) {
            res.json({
                success: true,
                message: result.message,
                template_id: result.template_id
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }

    } catch (error) {
        console.error('Error saving SMS template:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save template',
            details: error.message
        });
    }
});

/**
 * DELETE /sms-config/templates/:id - Delete SMS template
 */
router.delete('/templates/:id', requireAuth, requireRole(['admin']), async (req, res) => {
    try {
        const templateId = parseInt(req.params.id);
        
        if (!templateId || templateId < 1) {
            return res.status(400).json({
                success: false,
                error: 'Valid template ID is required'
            });
        }

        const result = await smsTemplateService.deleteTemplate(templateId);

        if (result.success) {
            res.json({
                success: true,
                message: result.message
            });
        } else {
            res.status(400).json({
                success: false,
                error: result.error
            });
        }

    } catch (error) {
        console.error('Error deleting SMS template:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete template',
            details: error.message
        });
    }
});

/**
 * POST /sms-config/templates/preview - Preview template with sample data
 */
router.post('/templates/preview', requireAuth, requireRole(['admin', 'teacher']), [
    body('template')
        .notEmpty()
        .withMessage('Template content is required'),
    body('sample_data')
        .optional()
        .isObject()
        .withMessage('Sample data must be an object')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const { template, sample_data = {} } = req.body;

        const result = smsTemplateService.previewTemplate(template, sample_data);

        res.json({
            success: result.success,
            preview: result.preview,
            variables: result.variables,
            sample_data: result.sampleData,
            error: result.error
        });

    } catch (error) {
        console.error('Error previewing template:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to preview template',
            details: error.message
        });
    }
});

/**
 * GET /sms-config/statistics - Get SMS usage statistics
 */
router.get('/statistics', requireAuth, requireRole(['admin', 'teacher']), async (req, res) => {
    try {
        const { date_from, date_to, teacher_id, template_type } = req.query;

        const filters = {};
        if (date_from) filters.date_from = date_from;
        if (date_to) filters.date_to = date_to;
        if (teacher_id) filters.teacher_id = parseInt(teacher_id);
        if (template_type) filters.template_type = template_type;

        const stats = await smsService.getStatistics(filters);

        if (stats.success) {
            res.json({
                success: true,
                statistics: stats
            });
        } else {
            res.status(400).json({
                success: false,
                error: stats.error
            });
        }

    } catch (error) {
        console.error('Error getting SMS statistics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get statistics',
            details: error.message
        });
    }
});

module.exports = router;
