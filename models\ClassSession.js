const dbConnection = require('../database/connection');
const QRCode = require('qrcode');
const crypto = require('crypto');
const moment = require('moment');

class ClassSession {
    constructor(data) {
        this.session_id = data.session_id;
        this.subject_id = data.subject_id;
        this.teacher_id = data.teacher_id;
        this.room_number = data.room_number;
        this.date_time = data.date_time;
        this.qr_code_data = data.qr_code_data;
        this.qr_expiry = data.qr_expiry;
        this.status = data.status;
        this.created_at = data.created_at;
        
        // For joined queries
        this.subject_name = data.subject_name;
        this.subject_code = data.subject_code;
        this.teacher_name = data.teacher_name;
        this.grade_level = data.grade_level;
    }

    /**
     * Create a new class session
     * @param {Object} sessionData - Session data
     * @returns {Promise<ClassSession>}
     */
    static async create(sessionData) {
        const { subject_id, teacher_id, room_number, date_time } = sessionData;

        // Generate initial QR code data and expiry
        const qrData = ClassSession.generateQRData({
            session_id: null, // Will be updated after insertion
            subject_id,
            teacher_id,
            timestamp: new Date().toISOString()
        });

        const qrExpiry = ClassSession.calculateExpiry();

        const result = await dbConnection.run(
            `INSERT INTO class_sessions (subject_id, teacher_id, room_number, date_time, qr_code_data, qr_expiry, status)
             VALUES (?, ?, ?, ?, ?, ?, 'active')`,
            [subject_id, teacher_id, room_number, date_time, qrData, qrExpiry.toISOString()]
        );

        // Update QR code data with actual session_id
        const finalQrData = ClassSession.generateQRData({
            session_id: result.lastID,
            subject_id,
            teacher_id,
            timestamp: new Date().toISOString()
        });

        await dbConnection.run(
            'UPDATE class_sessions SET qr_code_data = ? WHERE session_id = ?',
            [finalQrData, result.lastID]
        );

        return await ClassSession.findById(result.lastID);
    }

    /**
     * Generate QR code data with security hash
     * @param {Object} data - Data to include in QR code
     * @returns {string} JSON string for QR code
     */
    static generateQRData(data) {
        const qrPayload = {
            session_id: data.session_id,
            subject_id: data.subject_id,
            teacher_id: data.teacher_id,
            timestamp: data.timestamp,
            type: 'SESSION'
        };

        // Generate validation hash
        const hashInput = `${qrPayload.session_id}-${qrPayload.subject_id}-${qrPayload.teacher_id}-${qrPayload.timestamp}`;
        qrPayload.validation_hash = crypto.createHash('sha256').update(hashInput).digest('hex').substring(0, 16);

        return JSON.stringify(qrPayload);
    }

    /**
     * Calculate QR code expiry time (15 minutes from now)
     * @returns {Date}
     */
    static calculateExpiry() {
        return moment().add(15, 'minutes').toDate();
    }

    /**
     * Validate QR code data
     * @param {string} qrData - QR code data to validate
     * @returns {Object} Validation result
     */
    static validateQRData(qrData) {
        try {
            const payload = JSON.parse(qrData);
            
            // Check required fields
            if (!payload.session_id || !payload.subject_id || !payload.teacher_id || 
                !payload.timestamp || !payload.validation_hash || payload.type !== 'SESSION') {
                return { valid: false, error: 'Invalid QR code format' };
            }

            // Validate hash
            const hashInput = `${payload.session_id}-${payload.subject_id}-${payload.teacher_id}-${payload.timestamp}`;
            const expectedHash = crypto.createHash('sha256').update(hashInput).digest('hex').substring(0, 16);
            
            if (payload.validation_hash !== expectedHash) {
                return { valid: false, error: 'Invalid QR code signature' };
            }

            return { valid: true, payload };
        } catch (error) {
            return { valid: false, error: 'Invalid QR code data' };
        }
    }

    /**
     * Find session by ID with joined data
     * @param {number} id - Session ID
     * @returns {Promise<ClassSession|null>}
     */
    static async findById(id) {
        const row = await dbConnection.get(
            `SELECT cs.*, s.subject_name, s.subject_code, s.grade_level, u.full_name as teacher_name
             FROM class_sessions cs
             LEFT JOIN subjects s ON cs.subject_id = s.subject_id
             LEFT JOIN users u ON cs.teacher_id = u.user_id
             WHERE cs.session_id = ?`,
            [id]
        );

        return row ? new ClassSession(row) : null;
    }

    /**
     * Get all sessions with optional filters
     * @param {Object} filters - Filter options
     * @returns {Promise<ClassSession[]>}
     */
    static async findAll(filters = {}) {
        let query = `SELECT cs.*, s.subject_name, s.subject_code, s.grade_level, u.full_name as teacher_name
                     FROM class_sessions cs
                     LEFT JOIN subjects s ON cs.subject_id = s.subject_id
                     LEFT JOIN users u ON cs.teacher_id = u.user_id`;
        const params = [];
        const conditions = [];

        if (filters.status) {
            conditions.push('cs.status = ?');
            params.push(filters.status);
        }

        if (filters.teacher_id) {
            conditions.push('cs.teacher_id = ?');
            params.push(filters.teacher_id);
        }

        if (filters.subject_id) {
            conditions.push('cs.subject_id = ?');
            params.push(filters.subject_id);
        }

        if (filters.date_from) {
            conditions.push('DATE(cs.date_time) >= ?');
            params.push(filters.date_from);
        }

        if (filters.date_to) {
            conditions.push('DATE(cs.date_time) <= ?');
            params.push(filters.date_to);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' ORDER BY cs.date_time DESC';

        const rows = await dbConnection.all(query, params);
        return rows.map(row => new ClassSession(row));
    }

    /**
     * Get active sessions
     * @returns {Promise<ClassSession[]>}
     */
    static async findActive() {
        return await ClassSession.findAll({ status: 'active' });
    }

    /**
     * Get sessions by teacher
     * @param {number} teacherId - Teacher ID
     * @param {Object} options - Additional options
     * @returns {Promise<ClassSession[]>}
     */
    static async findByTeacher(teacherId, options = {}) {
        const filters = { teacher_id: teacherId, ...options };
        return await ClassSession.findAll(filters);
    }

    /**
     * Get today's sessions
     * @returns {Promise<ClassSession[]>}
     */
    static async findToday() {
        const today = moment().format('YYYY-MM-DD');
        return await ClassSession.findAll({ 
            date_from: today, 
            date_to: today 
        });
    }

    /**
     * Check if QR code is expired
     * @returns {boolean}
     */
    isQRExpired() {
        return new Date() > new Date(this.qr_expiry);
    }

    /**
     * Refresh QR code with new expiry
     * @returns {Promise<ClassSession>}
     */
    async refreshQR() {
        const newQrData = ClassSession.generateQRData({
            session_id: this.session_id,
            subject_id: this.subject_id,
            teacher_id: this.teacher_id,
            timestamp: new Date().toISOString()
        });

        const newExpiry = ClassSession.calculateExpiry();

        await dbConnection.run(
            'UPDATE class_sessions SET qr_code_data = ?, qr_expiry = ? WHERE session_id = ?',
            [newQrData, newExpiry.toISOString(), this.session_id]
        );

        return await ClassSession.findById(this.session_id);
    }

    /**
     * Update session status
     * @param {string} status - New status
     * @returns {Promise<ClassSession>}
     */
    async updateStatus(status) {
        const validStatuses = ['active', 'expired', 'completed'];
        if (!validStatuses.includes(status)) {
            throw new Error('Invalid status');
        }

        await dbConnection.run(
            'UPDATE class_sessions SET status = ? WHERE session_id = ?',
            [status, this.session_id]
        );

        return await ClassSession.findById(this.session_id);
    }

    /**
     * Update session
     * @param {Object} updateData - Data to update
     * @returns {Promise<ClassSession>}
     */
    async update(updateData) {
        const fields = [];
        const values = [];

        for (const [key, value] of Object.entries(updateData)) {
            if (key !== 'session_id' && key !== 'created_at' && 
                key !== 'subject_name' && key !== 'teacher_name' && key !== 'grade_level') {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (fields.length === 0) {
            throw new Error('No valid fields to update');
        }

        values.push(this.session_id);

        await dbConnection.run(
            `UPDATE class_sessions SET ${fields.join(', ')} WHERE session_id = ?`,
            values
        );

        return await ClassSession.findById(this.session_id);
    }

    /**
     * Delete session
     * @returns {Promise<boolean>}
     */
    async delete() {
        const result = await dbConnection.run(
            'DELETE FROM class_sessions WHERE session_id = ?',
            [this.session_id]
        );

        return result.changes > 0;
    }

    /**
     * Generate QR code image
     * @returns {Promise<string>} Base64 encoded QR code image
     */
    async generateQRCode() {
        try {
            return await QRCode.toDataURL(this.qr_code_data, {
                width: 300,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            });
        } catch (error) {
            throw new Error('Failed to generate QR code: ' + error.message);
        }
    }

    /**
     * Get session attendance count
     * @returns {Promise<Object>}
     */
    async getAttendanceStats() {
        const stats = await dbConnection.get(
            `SELECT 
                COUNT(*) as total_attendance,
                COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count
             FROM attendance 
             WHERE session_id = ?`,
            [this.session_id]
        );

        return {
            total_attendance: stats.total_attendance || 0,
            present_count: stats.present_count || 0,
            late_count: stats.late_count || 0
        };
    }

    /**
     * Convert to JSON
     * @returns {Object}
     */
    toJSON() {
        return {
            session_id: this.session_id,
            subject_id: this.subject_id,
            subject_name: this.subject_name,
            subject_code: this.subject_code,
            grade_level: this.grade_level,
            teacher_id: this.teacher_id,
            teacher_name: this.teacher_name,
            room_number: this.room_number,
            date_time: this.date_time,
            qr_code_data: this.qr_code_data,
            qr_expiry: this.qr_expiry,
            status: this.status,
            created_at: this.created_at,
            is_qr_expired: this.isQRExpired()
        };
    }
}

module.exports = ClassSession;
