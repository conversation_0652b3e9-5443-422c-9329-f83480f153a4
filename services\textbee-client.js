const axios = require('axios');
const dbConnection = require('../database/connection');

/**
 * TextBee SMS Gateway Client
 * Handles communication with TextBee open-source SMS gateway
 */
class TextBeeClient {
    constructor() {
        this.config = null;
        this.isConnected = false;
        this.lastHealthCheck = null;
        this.healthCheckInterval = 30000; // 30 seconds
    }

    /**
     * Initialize the TextBee client with configuration
     */
    async initialize() {
        try {
            await this.loadConfig();
            await this.checkHealth();
            console.log('TextBee client initialized successfully');
        } catch (error) {
            console.error('Failed to initialize TextBee client:', error.message);
            throw error;
        }
    }

    /**
     * Load SMS configuration from database
     */
    async loadConfig() {
        try {
            const config = await dbConnection.get('SELECT * FROM sms_config LIMIT 1');
            if (!config) {
                throw new Error('SMS configuration not found in database');
            }
            this.config = config;
        } catch (error) {
            console.error('Error loading SMS configuration:', error.message);
            throw error;
        }
    }

    /**
     * Check TextBee server health
     */
    async checkHealth() {
        try {
            if (!this.config) {
                await this.loadConfig();
            }

            const response = await axios.get(`${this.config.textbee_server_url}/health`, {
                timeout: 5000,
                headers: {
                    'Content-Type': 'application/json',
                    ...(this.config.textbee_api_key && { 'Authorization': `Bearer ${this.config.textbee_api_key}` })
                }
            });

            this.isConnected = response.status === 200;
            this.lastHealthCheck = new Date();
            
            return {
                success: true,
                status: response.data,
                connected: this.isConnected
            };
        } catch (error) {
            this.isConnected = false;
            this.lastHealthCheck = new Date();
            
            console.error('TextBee health check failed:', error.message);
            return {
                success: false,
                error: error.message,
                connected: false
            };
        }
    }

    /**
     * Send SMS message via TextBee
     * @param {Object} messageData - SMS message data
     * @returns {Promise<Object>} - Send result
     */
    async sendSMS(messageData) {
        try {
            const { recipient, message, sender, priority = 5 } = messageData;

            if (!this.config) {
                await this.loadConfig();
            }

            if (!this.config.enable_sms) {
                return {
                    success: false,
                    error: 'SMS sending is disabled in configuration'
                };
            }

            // Check if TextBee is available
            if (!this.isConnected) {
                const healthCheck = await this.checkHealth();
                if (!healthCheck.success) {
                    return {
                        success: false,
                        error: 'TextBee server is not available',
                        shouldRetry: true
                    };
                }
            }

            // Prepare SMS payload for TextBee
            const smsPayload = {
                recipient: this.formatPhoneNumber(recipient),
                message: message,
                sender: sender || this.config.default_sender_name,
                priority: priority,
                delivery_report: this.config.enable_delivery_reports
            };

            // Send SMS to TextBee
            const response = await axios.post(
                `${this.config.textbee_server_url}/api/sms/send`,
                smsPayload,
                {
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json',
                        ...(this.config.textbee_api_key && { 'Authorization': `Bearer ${this.config.textbee_api_key}` })
                    }
                }
            );

            if (response.status === 200 || response.status === 201) {
                return {
                    success: true,
                    messageId: response.data.messageId || response.data.id,
                    status: response.data.status || 'sent',
                    response: response.data
                };
            } else {
                return {
                    success: false,
                    error: `TextBee returned status ${response.status}`,
                    response: response.data,
                    shouldRetry: response.status >= 500
                };
            }

        } catch (error) {
            console.error('Error sending SMS via TextBee:', error.message);
            
            // Determine if this is a retryable error
            const shouldRetry = this.isRetryableError(error);
            
            return {
                success: false,
                error: error.message,
                shouldRetry: shouldRetry,
                errorCode: error.code
            };
        }
    }

    /**
     * Send multiple SMS messages in batch
     * @param {Array} messages - Array of message objects
     * @returns {Promise<Object>} - Batch send result
     */
    async sendBatchSMS(messages) {
        try {
            if (!this.config) {
                await this.loadConfig();
            }

            if (!this.config.enable_sms) {
                return {
                    success: false,
                    error: 'SMS sending is disabled in configuration'
                };
            }

            // Check if TextBee supports batch sending
            const batchPayload = {
                messages: messages.map(msg => ({
                    recipient: this.formatPhoneNumber(msg.recipient),
                    message: msg.message,
                    sender: msg.sender || this.config.default_sender_name,
                    priority: msg.priority || 5
                })),
                delivery_report: this.config.enable_delivery_reports
            };

            const response = await axios.post(
                `${this.config.textbee_server_url}/api/sms/batch`,
                batchPayload,
                {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json',
                        ...(this.config.textbee_api_key && { 'Authorization': `Bearer ${this.config.textbee_api_key}` })
                    }
                }
            );

            return {
                success: true,
                batchId: response.data.batchId,
                results: response.data.results || [],
                response: response.data
            };

        } catch (error) {
            console.error('Error sending batch SMS:', error.message);
            
            // If batch is not supported, fall back to individual sends
            if (error.response && error.response.status === 404) {
                console.log('Batch SMS not supported, falling back to individual sends');
                return await this.sendIndividualSMS(messages);
            }
            
            return {
                success: false,
                error: error.message,
                shouldRetry: this.isRetryableError(error)
            };
        }
    }

    /**
     * Send messages individually when batch is not supported
     * @param {Array} messages - Array of message objects
     * @returns {Promise<Object>} - Individual send results
     */
    async sendIndividualSMS(messages) {
        const results = [];
        let successCount = 0;
        let failureCount = 0;

        for (const message of messages) {
            try {
                const result = await this.sendSMS(message);
                results.push({
                    recipient: message.recipient,
                    success: result.success,
                    messageId: result.messageId,
                    error: result.error
                });

                if (result.success) {
                    successCount++;
                } else {
                    failureCount++;
                }

                // Add small delay between sends to avoid overwhelming the server
                await this.delay(100);

            } catch (error) {
                results.push({
                    recipient: message.recipient,
                    success: false,
                    error: error.message
                });
                failureCount++;
            }
        }

        return {
            success: successCount > 0,
            totalSent: successCount,
            totalFailed: failureCount,
            results: results
        };
    }

    /**
     * Get delivery status for a message
     * @param {string} messageId - TextBee message ID
     * @returns {Promise<Object>} - Delivery status
     */
    async getDeliveryStatus(messageId) {
        try {
            if (!this.config) {
                await this.loadConfig();
            }

            const response = await axios.get(
                `${this.config.textbee_server_url}/api/sms/status/${messageId}`,
                {
                    timeout: 5000,
                    headers: {
                        'Content-Type': 'application/json',
                        ...(this.config.textbee_api_key && { 'Authorization': `Bearer ${this.config.textbee_api_key}` })
                    }
                }
            );

            return {
                success: true,
                status: response.data.status,
                deliveredAt: response.data.deliveredAt,
                response: response.data
            };

        } catch (error) {
            console.error('Error getting delivery status:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Format phone number for TextBee
     * @param {string} phoneNumber - Raw phone number
     * @returns {string} - Formatted phone number
     */
    formatPhoneNumber(phoneNumber) {
        // Remove all non-digit characters
        let cleaned = phoneNumber.replace(/\D/g, '');
        
        // Add country code if not present (assuming Philippines +63)
        if (cleaned.startsWith('09')) {
            cleaned = '63' + cleaned.substring(1);
        } else if (!cleaned.startsWith('63')) {
            cleaned = '63' + cleaned;
        }
        
        return '+' + cleaned;
    }

    /**
     * Determine if an error is retryable
     * @param {Error} error - The error object
     * @returns {boolean} - Whether the error is retryable
     */
    isRetryableError(error) {
        // Network errors are retryable
        if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT' || error.code === 'ENOTFOUND') {
            return true;
        }
        
        // HTTP 5xx errors are retryable
        if (error.response && error.response.status >= 500) {
            return true;
        }
        
        // Rate limiting (429) is retryable
        if (error.response && error.response.status === 429) {
            return true;
        }
        
        return false;
    }

    /**
     * Utility function to add delay
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} - Promise that resolves after delay
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get TextBee server information
     * @returns {Promise<Object>} - Server information
     */
    async getServerInfo() {
        try {
            if (!this.config) {
                await this.loadConfig();
            }

            const response = await axios.get(`${this.config.textbee_server_url}/api/info`, {
                timeout: 5000,
                headers: {
                    'Content-Type': 'application/json',
                    ...(this.config.textbee_api_key && { 'Authorization': `Bearer ${this.config.textbee_api_key}` })
                }
            });

            return {
                success: true,
                info: response.data
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create singleton instance
const textBeeClient = new TextBeeClient();

module.exports = textBeeClient;
