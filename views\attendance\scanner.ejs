<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - QR Attendance System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/scanner.css">
    
    <style>
        .scanner-container {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }
        
        #video {
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            border-radius: 10px;
        }
        
        .scanner-frame {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 250px;
            border: 3px solid #28a745;
            border-radius: 10px;
            box-shadow: 0 0 0 9999px rgba(0,0,0,0.3);
        }
        
        .scanner-frame::before,
        .scanner-frame::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 30px;
            border: 3px solid #28a745;
        }
        
        .scanner-frame::before {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }
        
        .scanner-frame::after {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }
        
        .scanning-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #28a745, transparent);
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            0% { top: 0; }
            100% { top: 100%; }
        }
        
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-scanning {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        .attendance-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .student-card {
            transition: all 0.3s ease;
            border-left: 4px solid #28a745;
        }
        
        .student-card.late {
            border-left-color: #ffc107;
        }
        
        .student-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .scan-success {
            animation: pulse 0.5s ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .mobile-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .scanner-frame {
                width: 200px;
                height: 200px;
            }
            
            .container-fluid {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <%- include('../layout', { user: user }) %>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Scanner Section -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-qr-code-scan me-2"></i>QR Code Scanner
                        </h5>
                        <div class="d-flex gap-2">
                            <select id="sessionSelect" class="form-select form-select-sm" style="width: auto;">
                                <option value="">Select Session</option>
                                <% activeSessions.forEach(session => { %>
                                    <option value="<%= session.session_id %>" 
                                            <%= selectedSessionId == session.session_id ? 'selected' : '' %>>
                                        <%= session.subject_name %> - <%= session.room_number %>
                                    </option>
                                <% }); %>
                            </select>
                            <button id="toggleCamera" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-camera-video"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Session Info -->
                        <div id="sessionInfo" class="alert alert-info d-none">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong id="sessionSubject"></strong><br>
                                    <small id="sessionDetails"></small>
                                </div>
                                <div class="text-end">
                                    <div class="badge bg-success" id="attendanceCount">0 Present</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Camera Section -->
                        <div class="scanner-container">
                            <video id="video" autoplay muted playsinline></video>
                            <div class="scanner-overlay">
                                <div class="scanner-frame">
                                    <div class="scanning-line"></div>
                                </div>
                                <div id="statusIndicator" class="status-indicator status-scanning d-none">
                                    Scanning...
                                </div>
                            </div>
                        </div>
                        
                        <!-- Camera Controls -->
                        <div class="text-center mt-3">
                            <button id="startCamera" class="btn btn-success me-2">
                                <i class="bi bi-camera-video"></i> Start Camera
                            </button>
                            <button id="stopCamera" class="btn btn-danger me-2 d-none">
                                <i class="bi bi-camera-video-off"></i> Stop Camera
                            </button>
                            <button id="switchCamera" class="btn btn-outline-secondary d-none">
                                <i class="bi bi-arrow-repeat"></i> Switch Camera
                            </button>
                        </div>
                        
                        <!-- Manual Entry -->
                        <div class="mt-3">
                            <button class="btn btn-outline-warning btn-sm" data-bs-toggle="collapse" data-bs-target="#manualEntry">
                                <i class="bi bi-pencil"></i> Manual Entry
                            </button>
                            <div id="manualEntry" class="collapse mt-2">
                                <div class="input-group">
                                    <input type="text" id="manualQRInput" class="form-control" placeholder="Enter QR code data manually">
                                    <button id="submitManual" class="btn btn-outline-primary">Submit</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance List Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-people me-2"></i>Live Attendance
                        </h5>
                        <div class="d-flex gap-2">
                            <span class="badge bg-success" id="presentCount">0 Present</span>
                            <span class="badge bg-warning" id="lateCount">0 Late</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="attendanceList" class="attendance-list">
                            <div class="text-center p-4 text-muted">
                                <i class="bi bi-qr-code-scan display-4"></i>
                                <p class="mt-2">Select a session and start scanning to see attendance</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Success/Error Modals -->
    <div class="modal fade" id="scanResultModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scanResultTitle">Scan Result</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="scanResultBody">
                    <!-- Dynamic content -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mobile Controls -->
    <div class="mobile-controls d-lg-none">
        <div class="btn-group" role="group">
            <button id="mobileStartCamera" class="btn btn-success">
                <i class="bi bi-camera-video"></i>
            </button>
            <button id="mobileStopCamera" class="btn btn-danger d-none">
                <i class="bi bi-camera-video-off"></i>
            </button>
            <button id="mobileSwitchCamera" class="btn btn-outline-secondary d-none">
                <i class="bi bi-arrow-repeat"></i>
            </button>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jsQR Library -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>

    <script>
        class QRScanner {
            constructor() {
                this.video = document.getElementById('video');
                this.canvas = document.createElement('canvas');
                this.context = this.canvas.getContext('2d');
                this.stream = null;
                this.scanning = false;
                this.currentSessionId = null;
                this.cameras = [];
                this.currentCameraIndex = 0;
                this.lastScanTime = 0;
                this.scanCooldown = 2000; // 2 seconds between scans

                this.initializeElements();
                this.bindEvents();
                this.loadSessionFromURL();
            }

            initializeElements() {
                this.sessionSelect = document.getElementById('sessionSelect');
                this.sessionInfo = document.getElementById('sessionInfo');
                this.sessionSubject = document.getElementById('sessionSubject');
                this.sessionDetails = document.getElementById('sessionDetails');
                this.attendanceCount = document.getElementById('attendanceCount');
                this.presentCount = document.getElementById('presentCount');
                this.lateCount = document.getElementById('lateCount');
                this.attendanceList = document.getElementById('attendanceList');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.startCameraBtn = document.getElementById('startCamera');
                this.stopCameraBtn = document.getElementById('stopCamera');
                this.switchCameraBtn = document.getElementById('switchCamera');
                this.manualQRInput = document.getElementById('manualQRInput');
                this.submitManualBtn = document.getElementById('submitManual');

                // Mobile controls
                this.mobileStartCamera = document.getElementById('mobileStartCamera');
                this.mobileStopCamera = document.getElementById('mobileStopCamera');
                this.mobileSwitchCamera = document.getElementById('mobileSwitchCamera');
            }

            bindEvents() {
                // Session selection
                this.sessionSelect.addEventListener('change', (e) => {
                    this.selectSession(e.target.value);
                });

                // Camera controls
                this.startCameraBtn.addEventListener('click', () => this.startCamera());
                this.stopCameraBtn.addEventListener('click', () => this.stopCamera());
                this.switchCameraBtn.addEventListener('click', () => this.switchCamera());

                // Mobile controls
                this.mobileStartCamera.addEventListener('click', () => this.startCamera());
                this.mobileStopCamera.addEventListener('click', () => this.stopCamera());
                this.mobileSwitchCamera.addEventListener('click', () => this.switchCamera());

                // Manual entry
                this.submitManualBtn.addEventListener('click', () => this.processManualEntry());
                this.manualQRInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.processManualEntry();
                    }
                });

                // Video events
                this.video.addEventListener('loadedmetadata', () => {
                    this.canvas.width = this.video.videoWidth;
                    this.canvas.height = this.video.videoHeight;
                });
            }

            loadSessionFromURL() {
                const urlParams = new URLSearchParams(window.location.search);
                const sessionId = urlParams.get('session_id');
                if (sessionId) {
                    this.sessionSelect.value = sessionId;
                    this.selectSession(sessionId);
                }
            }

            async selectSession(sessionId) {
                if (!sessionId) {
                    this.currentSessionId = null;
                    this.sessionInfo.classList.add('d-none');
                    this.clearAttendanceList();
                    return;
                }

                this.currentSessionId = sessionId;

                try {
                    const response = await fetch(`/attendance/session/${sessionId}`, {
                        headers: { 'Accept': 'application/json' }
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.updateSessionInfo(data.session);
                        this.updateAttendanceList(data.attendance);
                        this.updateAttendanceCounts(data);
                        this.sessionInfo.classList.remove('d-none');
                    } else {
                        this.showError('Failed to load session data');
                    }
                } catch (error) {
                    console.error('Error loading session:', error);
                    this.showError('Error loading session data');
                }
            }

            updateSessionInfo(session) {
                this.sessionSubject.textContent = session.subject_name;
                this.sessionDetails.innerHTML = `
                    Room: ${session.room_number} |
                    ${new Date(session.date_time).toLocaleString()}
                `;
            }

            updateAttendanceList(attendance) {
                if (attendance.length === 0) {
                    this.attendanceList.innerHTML = `
                        <div class="text-center p-4 text-muted">
                            <i class="bi bi-people display-4"></i>
                            <p class="mt-2">No attendance recorded yet</p>
                        </div>
                    `;
                    return;
                }

                const attendanceHTML = attendance.map(record => `
                    <div class="student-card card mb-2 ${record.status === 'late' ? 'late' : ''}">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${record.student_name}</strong>
                                    <br>
                                    <small class="text-muted">LRN: ${record.student_lrn}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge ${record.status === 'late' ? 'bg-warning' : 'bg-success'}">
                                        ${record.status.toUpperCase()}
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        ${new Date(record.timestamp).toLocaleTimeString()}
                                    </small>
                                </div>
                            </div>
                            ${record.remarks ? `<small class="text-warning">${record.remarks}</small>` : ''}
                        </div>
                    </div>
                `).join('');

                this.attendanceList.innerHTML = attendanceHTML;
            }

            updateAttendanceCounts(data) {
                this.attendanceCount.textContent = `${data.total_attendance} Present`;
                this.presentCount.textContent = `${data.total_present} Present`;
                this.lateCount.textContent = `${data.total_late} Late`;
            }

            clearAttendanceList() {
                this.attendanceList.innerHTML = `
                    <div class="text-center p-4 text-muted">
                        <i class="bi bi-qr-code-scan display-4"></i>
                        <p class="mt-2">Select a session and start scanning to see attendance</p>
                    </div>
                `;
                this.presentCount.textContent = '0 Present';
                this.lateCount.textContent = '0 Late';
                this.attendanceCount.textContent = '0 Present';
            }

            async getCameras() {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    this.cameras = devices.filter(device => device.kind === 'videoinput');

                    if (this.cameras.length > 1) {
                        this.switchCameraBtn.classList.remove('d-none');
                        this.mobileSwitchCamera.classList.remove('d-none');
                    }
                } catch (error) {
                    console.error('Error getting cameras:', error);
                }
            }

            async startCamera() {
                if (!this.currentSessionId) {
                    this.showError('Please select a session first');
                    return;
                }

                try {
                    await this.getCameras();

                    const constraints = {
                        video: {
                            facingMode: 'environment', // Prefer back camera
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    };

                    // If we have multiple cameras and user selected one
                    if (this.cameras.length > 0 && this.currentCameraIndex < this.cameras.length) {
                        constraints.video.deviceId = { exact: this.cameras[this.currentCameraIndex].deviceId };
                    }

                    this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                    this.video.srcObject = this.stream;

                    this.video.addEventListener('loadeddata', () => {
                        this.startScanning();
                    });

                    this.updateCameraControls(true);
                    this.statusIndicator.classList.remove('d-none');
                    this.statusIndicator.className = 'status-indicator status-scanning';
                    this.statusIndicator.textContent = 'Scanning...';

                } catch (error) {
                    console.error('Error starting camera:', error);
                    this.showError('Failed to access camera. Please check permissions.');
                }
            }

            stopCamera() {
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                }

                this.video.srcObject = null;
                this.scanning = false;
                this.updateCameraControls(false);
                this.statusIndicator.classList.add('d-none');
            }

            async switchCamera() {
                if (this.cameras.length <= 1) return;

                this.stopCamera();
                this.currentCameraIndex = (this.currentCameraIndex + 1) % this.cameras.length;

                // Small delay to ensure camera is properly released
                setTimeout(() => {
                    this.startCamera();
                }, 500);
            }

            updateCameraControls(isActive) {
                if (isActive) {
                    this.startCameraBtn.classList.add('d-none');
                    this.stopCameraBtn.classList.remove('d-none');
                    this.mobileStartCamera.classList.add('d-none');
                    this.mobileStopCamera.classList.remove('d-none');
                } else {
                    this.startCameraBtn.classList.remove('d-none');
                    this.stopCameraBtn.classList.add('d-none');
                    this.mobileStartCamera.classList.remove('d-none');
                    this.mobileStopCamera.classList.add('d-none');
                }
            }

            startScanning() {
                this.scanning = true;
                this.scanFrame();
            }

            scanFrame() {
                if (!this.scanning || !this.video.videoWidth || !this.video.videoHeight) {
                    if (this.scanning) {
                        requestAnimationFrame(() => this.scanFrame());
                    }
                    return;
                }

                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;
                this.context.drawImage(this.video, 0, 0);

                const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height);

                if (code && this.canScan()) {
                    this.processQRCode(code.data);
                }

                if (this.scanning) {
                    requestAnimationFrame(() => this.scanFrame());
                }
            }

            canScan() {
                const now = Date.now();
                if (now - this.lastScanTime < this.scanCooldown) {
                    return false;
                }
                this.lastScanTime = now;
                return true;
            }

            async processQRCode(qrData) {
                if (!this.currentSessionId) {
                    this.showError('No session selected');
                    return;
                }

                try {
                    // Parse QR data to determine if it's student or session QR
                    let studentQRData, sessionQRData;

                    try {
                        const parsedData = JSON.parse(qrData);
                        if (parsedData.type === 'STUDENT') {
                            studentQRData = qrData;
                            // Get session QR from current session
                            const sessionResponse = await fetch(`/sessions/${this.currentSessionId}/qr`);
                            const sessionData = await sessionResponse.json();
                            if (sessionData.success) {
                                sessionQRData = sessionData.qr_data;
                            } else {
                                throw new Error('Failed to get session QR data');
                            }
                        } else if (parsedData.type === 'SESSION') {
                            sessionQRData = qrData;
                            this.showError('Please scan a student QR code, not a session QR code');
                            return;
                        } else {
                            throw new Error('Unknown QR code type');
                        }
                    } catch (parseError) {
                        this.showError('Invalid QR code format');
                        return;
                    }

                    // Show scanning feedback
                    this.showScanningFeedback();

                    // Submit attendance
                    const response = await fetch('/attendance/scan', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            student_qr_data: studentQRData,
                            session_qr_data: sessionQRData
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showScanSuccess(result);
                        // Refresh attendance list
                        await this.selectSession(this.currentSessionId);
                    } else {
                        this.showScanError(result.error, result.details);
                    }

                } catch (error) {
                    console.error('Error processing QR code:', error);
                    this.showError('Failed to process QR code');
                }
            }

            async processManualEntry() {
                const qrData = this.manualQRInput.value.trim();
                if (!qrData) {
                    this.showError('Please enter QR code data');
                    return;
                }

                await this.processQRCode(qrData);
                this.manualQRInput.value = '';
            }

            showScanningFeedback() {
                this.statusIndicator.className = 'status-indicator status-scanning';
                this.statusIndicator.textContent = 'Processing...';

                // Add visual feedback to scanner frame
                const scannerFrame = document.querySelector('.scanner-frame');
                scannerFrame.style.borderColor = '#ffc107';

                setTimeout(() => {
                    scannerFrame.style.borderColor = '#28a745';
                }, 1000);
            }

            showScanSuccess(result) {
                // Show success modal
                const modal = new bootstrap.Modal(document.getElementById('scanResultModal'));
                document.getElementById('scanResultTitle').textContent = 'Attendance Recorded';
                document.getElementById('scanResultBody').innerHTML = `
                    <div class="text-center">
                        <i class="bi bi-check-circle-fill text-success display-4"></i>
                        <h5 class="mt-3">${result.student.name}</h5>
                        <p class="text-muted">LRN: ${result.student.lrn}</p>
                        <div class="alert alert-success">
                            <strong>Status:</strong> ${result.status.toUpperCase()}
                            ${result.minutes_late > 0 ? `<br><small>Arrived ${result.minutes_late} minutes late</small>` : ''}
                        </div>
                    </div>
                `;
                modal.show();

                // Update status indicator
                this.statusIndicator.className = 'status-indicator status-scanning';
                this.statusIndicator.textContent = 'Scanning...';

                // Add success animation
                this.video.classList.add('scan-success');
                setTimeout(() => {
                    this.video.classList.remove('scan-success');
                }, 500);
            }

            showScanError(error, details) {
                // Show error modal
                const modal = new bootstrap.Modal(document.getElementById('scanResultModal'));
                document.getElementById('scanResultTitle').textContent = 'Scan Error';
                document.getElementById('scanResultBody').innerHTML = `
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle-fill text-danger display-4"></i>
                        <h5 class="mt-3 text-danger">Scan Failed</h5>
                        <div class="alert alert-danger">
                            <strong>${error}</strong>
                            ${details ? `<br><small>${details}</small>` : ''}
                        </div>
                    </div>
                `;
                modal.show();

                // Update status indicator
                this.statusIndicator.className = 'status-indicator status-error';
                this.statusIndicator.textContent = 'Error';

                setTimeout(() => {
                    this.statusIndicator.className = 'status-indicator status-scanning';
                    this.statusIndicator.textContent = 'Scanning...';
                }, 3000);
            }

            showError(message) {
                const modal = new bootstrap.Modal(document.getElementById('scanResultModal'));
                document.getElementById('scanResultTitle').textContent = 'Error';
                document.getElementById('scanResultBody').innerHTML = `
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle-fill text-warning display-4"></i>
                        <h5 class="mt-3 text-warning">Error</h5>
                        <div class="alert alert-warning">
                            ${message}
                        </div>
                    </div>
                `;
                modal.show();
            }

            // Auto-refresh attendance list every 30 seconds
            startAutoRefresh() {
                setInterval(() => {
                    if (this.currentSessionId) {
                        this.selectSession(this.currentSessionId);
                    }
                }, 30000);
            }

            // Cleanup when page is unloaded
            cleanup() {
                this.stopCamera();
            }
        }

        // Initialize scanner when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const scanner = new QRScanner();
            scanner.startAutoRefresh();

            // Cleanup on page unload
            window.addEventListener('beforeunload', () => {
                scanner.cleanup();
            });

            // Handle visibility change (mobile browsers)
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    scanner.stopCamera();
                }
            });
        });
    </script>
</body>
</html>
