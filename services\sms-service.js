const dbConnection = require('../database/connection');
const textBeeClient = require('./textbee-client');
const smsQueue = require('./sms-queue');
const smsTemplateService = require('./sms-templates');
const moment = require('moment');

/**
 * Comprehensive SMS Service Layer
 * Integrates queue management, template processing, delivery tracking, and TextBee communication
 */
class SMSService {
    constructor() {
        this.isInitialized = false;
        this.config = null;
    }

    /**
     * Initialize the SMS service
     */
    async initialize() {
        try {
            console.log('Initializing SMS Service...');
            
            // Initialize all components
            await textBeeClient.initialize();
            await smsQueue.initialize();
            await this.loadConfig();
            
            this.isInitialized = true;
            console.log('SMS Service initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize SMS Service:', error.message);
            throw error;
        }
    }

    /**
     * Load SMS configuration
     */
    async loadConfig() {
        try {
            const config = await dbConnection.get('SELECT * FROM sms_config LIMIT 1');
            if (!config) {
                throw new Error('SMS configuration not found');
            }
            this.config = config;
        } catch (error) {
            console.error('Error loading SMS configuration:', error.message);
            throw error;
        }
    }

    /**
     * Send attendance notification
     * @param {Object} attendanceData - Attendance data
     * @returns {Promise<Object>} - Send result
     */
    async sendAttendanceNotification(attendanceData) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            if (!this.config.enable_sms) {
                return {
                    success: false,
                    error: 'SMS notifications are disabled'
                };
            }

            const {
                student_id,
                student_name,
                parent_contact,
                subject_name,
                teacher_name,
                teacher_id,
                session_id,
                attendance_id,
                status = 'present',
                timestamp = new Date(),
                minutes_late = 0
            } = attendanceData;

            // Create message using template
            const messageResult = await smsTemplateService.createAttendanceMessage({
                student_name,
                subject_name,
                teacher_name,
                timestamp,
                status,
                minutes_late
            });

            if (!messageResult.success) {
                return {
                    success: false,
                    error: `Failed to create message: ${messageResult.error}`
                };
            }

            // Add to queue for processing
            const queueResult = await smsQueue.addToQueue({
                student_id,
                parent_contact,
                message_content: messageResult.message,
                template_id: messageResult.template?.template_id,
                priority: status === 'late' ? 3 : 5, // Higher priority for late arrivals
                teacher_id,
                session_id,
                attendance_id
            });

            return {
                success: queueResult.success,
                message: 'Attendance notification queued successfully',
                queueId: queueResult.queueId,
                messageContent: messageResult.message
            };

        } catch (error) {
            console.error('Error sending attendance notification:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Send absence notification
     * @param {Object} absenceData - Absence data
     * @returns {Promise<Object>} - Send result
     */
    async sendAbsenceNotification(absenceData) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            if (!this.config.enable_sms) {
                return {
                    success: false,
                    error: 'SMS notifications are disabled'
                };
            }

            const {
                student_id,
                student_name,
                parent_contact,
                subject_name,
                teacher_id,
                session_id,
                timestamp = new Date()
            } = absenceData;

            // Create message using template
            const messageResult = await smsTemplateService.createAbsenceMessage({
                student_name,
                subject_name,
                timestamp
            });

            if (!messageResult.success) {
                return {
                    success: false,
                    error: `Failed to create message: ${messageResult.error}`
                };
            }

            // Add to queue with higher priority for absences
            const queueResult = await smsQueue.addToQueue({
                student_id,
                parent_contact,
                message_content: messageResult.message,
                template_id: messageResult.template?.template_id,
                priority: 2, // High priority for absences
                teacher_id,
                session_id
            });

            return {
                success: queueResult.success,
                message: 'Absence notification queued successfully',
                queueId: queueResult.queueId,
                messageContent: messageResult.message
            };

        } catch (error) {
            console.error('Error sending absence notification:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Send announcement to multiple recipients
     * @param {Object} announcementData - Announcement data
     * @returns {Promise<Object>} - Send result
     */
    async sendAnnouncement(announcementData) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            if (!this.config.enable_sms) {
                return {
                    success: false,
                    error: 'SMS notifications are disabled'
                };
            }

            const {
                message,
                recipients = [], // Array of {student_id, parent_contact}
                is_emergency = false,
                teacher_id = null,
                grade_level = null,
                section = null
            } = announcementData;

            // If no specific recipients, get all active students
            let targetRecipients = recipients;
            if (targetRecipients.length === 0) {
                targetRecipients = await this.getStudentContacts({ grade_level, section });
            }

            if (targetRecipients.length === 0) {
                return {
                    success: false,
                    error: 'No recipients found'
                };
            }

            // Create message using template
            const messageResult = await smsTemplateService.createAnnouncementMessage({
                message,
                is_emergency
            });

            if (!messageResult.success) {
                return {
                    success: false,
                    error: `Failed to create message: ${messageResult.error}`
                };
            }

            // Add all messages to queue
            const queueMessages = targetRecipients.map(recipient => ({
                student_id: recipient.student_id,
                parent_contact: recipient.parent_contact,
                message_content: messageResult.message,
                template_id: messageResult.template?.template_id,
                priority: is_emergency ? 1 : 6, // Highest priority for emergencies
                teacher_id
            }));

            const batchResult = await smsQueue.addBatchToQueue(queueMessages);

            return {
                success: batchResult.success,
                message: `Announcement queued for ${batchResult.totalQueued} recipients`,
                totalQueued: batchResult.totalQueued,
                totalFailed: batchResult.totalFailed,
                messageContent: messageResult.message
            };

        } catch (error) {
            console.error('Error sending announcement:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Send custom SMS message
     * @param {Object} messageData - Custom message data
     * @returns {Promise<Object>} - Send result
     */
    async sendCustomMessage(messageData) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            if (!this.config.enable_sms) {
                return {
                    success: false,
                    error: 'SMS notifications are disabled'
                };
            }

            const {
                student_id,
                parent_contact,
                message_content,
                priority = 5,
                teacher_id = null,
                session_id = null,
                attendance_id = null
            } = messageData;

            // Validate required fields
            if (!student_id || !parent_contact || !message_content) {
                return {
                    success: false,
                    error: 'Missing required fields: student_id, parent_contact, message_content'
                };
            }

            // Add to queue
            const queueResult = await smsQueue.addToQueue({
                student_id,
                parent_contact,
                message_content,
                priority,
                teacher_id,
                session_id,
                attendance_id
            });

            return {
                success: queueResult.success,
                message: 'Custom message queued successfully',
                queueId: queueResult.queueId
            };

        } catch (error) {
            console.error('Error sending custom message:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get student contacts based on filters
     * @param {Object} filters - Filter criteria
     * @returns {Promise<Array>} - Array of student contacts
     */
    async getStudentContacts(filters = {}) {
        try {
            let sql = `
                SELECT DISTINCT s.student_id, s.parent_contact, s.first_name, s.last_name, s.grade_level, s.section
                FROM students s
                WHERE s.enrollment_status = 'active'
            `;
            const params = [];

            if (filters.grade_level) {
                sql += ' AND s.grade_level = ?';
                params.push(filters.grade_level);
            }

            if (filters.section) {
                sql += ' AND s.section = ?';
                params.push(filters.section);
            }

            if (filters.student_ids && filters.student_ids.length > 0) {
                const placeholders = filters.student_ids.map(() => '?').join(',');
                sql += ` AND s.student_id IN (${placeholders})`;
                params.push(...filters.student_ids);
            }

            sql += ' ORDER BY s.grade_level, s.section, s.last_name, s.first_name';

            const students = await dbConnection.all(sql, params);
            
            return students.map(student => ({
                student_id: student.student_id,
                parent_contact: student.parent_contact,
                student_name: `${student.first_name} ${student.last_name}`,
                grade_level: student.grade_level,
                section: student.section
            }));

        } catch (error) {
            console.error('Error getting student contacts:', error.message);
            return [];
        }
    }

    /**
     * Get SMS delivery status
     * @param {number} queueId - Queue ID or SMS log ID
     * @returns {Promise<Object>} - Delivery status
     */
    async getDeliveryStatus(queueId) {
        try {
            // Check queue status first
            const queueItem = await dbConnection.get(
                'SELECT * FROM sms_queue WHERE queue_id = ?',
                [queueId]
            );

            if (!queueItem) {
                return {
                    success: false,
                    error: 'Message not found'
                };
            }

            // Get latest log entry
            const logEntry = await dbConnection.get(
                'SELECT * FROM sms_logs WHERE queue_id = ? ORDER BY created_at DESC LIMIT 1',
                [queueId]
            );

            // If message has TextBee message ID, get delivery status from TextBee
            let textBeeStatus = null;
            if (queueItem.textbee_message_id) {
                textBeeStatus = await textBeeClient.getDeliveryStatus(queueItem.textbee_message_id);
            }

            return {
                success: true,
                queueStatus: queueItem.status,
                deliveryStatus: logEntry?.delivery_status,
                textBeeStatus: textBeeStatus,
                retryCount: queueItem.retry_count,
                lastError: queueItem.error_message,
                processedAt: queueItem.processed_at,
                createdAt: queueItem.created_at
            };

        } catch (error) {
            console.error('Error getting delivery status:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get SMS statistics
     * @param {Object} filters - Filter criteria
     * @returns {Promise<Object>} - SMS statistics
     */
    async getStatistics(filters = {}) {
        try {
            const { 
                date_from = moment().subtract(30, 'days').format('YYYY-MM-DD'),
                date_to = moment().format('YYYY-MM-DD'),
                teacher_id = null,
                template_type = null
            } = filters;

            // Get overall statistics
            const overallStats = await dbConnection.get(
                `SELECT 
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN delivery_status = 'sent' THEN 1 END) as sent_count,
                    COUNT(CASE WHEN delivery_status = 'delivered' THEN 1 END) as delivered_count,
                    COUNT(CASE WHEN delivery_status = 'failed' THEN 1 END) as failed_count,
                    COUNT(CASE WHEN delivery_status = 'pending' THEN 1 END) as pending_count
                 FROM sms_logs 
                 WHERE DATE(created_at) BETWEEN ? AND ?
                   ${teacher_id ? 'AND teacher_id = ?' : ''}`,
                teacher_id ? [date_from, date_to, teacher_id] : [date_from, date_to]
            );

            // Get daily statistics
            const dailyStats = await dbConnection.all(
                `SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as total,
                    COUNT(CASE WHEN delivery_status = 'delivered' THEN 1 END) as delivered,
                    COUNT(CASE WHEN delivery_status = 'failed' THEN 1 END) as failed
                 FROM sms_logs 
                 WHERE DATE(created_at) BETWEEN ? AND ?
                   ${teacher_id ? 'AND teacher_id = ?' : ''}
                 GROUP BY DATE(created_at)
                 ORDER BY date`,
                teacher_id ? [date_from, date_to, teacher_id] : [date_from, date_to]
            );

            // Get template usage statistics
            const templateStats = await dbConnection.all(
                `SELECT 
                    t.template_name,
                    t.template_type,
                    COUNT(l.sms_id) as usage_count
                 FROM sms_templates t
                 LEFT JOIN sms_logs l ON t.template_id = l.template_id 
                   AND DATE(l.created_at) BETWEEN ? AND ?
                   ${teacher_id ? 'AND l.teacher_id = ?' : ''}
                 WHERE t.is_active = 1
                   ${template_type ? 'AND t.template_type = ?' : ''}
                 GROUP BY t.template_id, t.template_name, t.template_type
                 ORDER BY usage_count DESC`,
                teacher_id 
                    ? (template_type ? [date_from, date_to, teacher_id, template_type] : [date_from, date_to, teacher_id])
                    : (template_type ? [date_from, date_to, template_type] : [date_from, date_to])
            );

            // Get queue statistics
            const queueStats = await smsQueue.getQueueStats();

            return {
                success: true,
                period: { from: date_from, to: date_to },
                overall: overallStats,
                daily: dailyStats,
                templates: templateStats,
                queue: queueStats.stats,
                deliveryRate: overallStats.total_messages > 0 
                    ? ((overallStats.delivered_count / overallStats.total_messages) * 100).toFixed(2)
                    : 0
            };

        } catch (error) {
            console.error('Error getting SMS statistics:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update SMS configuration
     * @param {Object} configData - Configuration data
     * @returns {Promise<Object>} - Update result
     */
    async updateConfiguration(configData) {
        try {
            const {
                textbee_server_url,
                textbee_api_key,
                default_sender_name,
                max_retry_attempts,
                retry_delay_minutes,
                queue_batch_size,
                enable_sms,
                enable_delivery_reports
            } = configData;

            await dbConnection.run(
                `UPDATE sms_config SET 
                    textbee_server_url = ?, textbee_api_key = ?, default_sender_name = ?,
                    max_retry_attempts = ?, retry_delay_minutes = ?, queue_batch_size = ?,
                    enable_sms = ?, enable_delivery_reports = ?, updated_at = CURRENT_TIMESTAMP`,
                [
                    textbee_server_url, textbee_api_key, default_sender_name,
                    max_retry_attempts, retry_delay_minutes, queue_batch_size,
                    enable_sms, enable_delivery_reports
                ]
            );

            // Reload configuration
            await this.loadConfig();
            await smsQueue.loadConfig();

            return {
                success: true,
                message: 'SMS configuration updated successfully'
            };

        } catch (error) {
            console.error('Error updating SMS configuration:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test SMS connectivity
     * @returns {Promise<Object>} - Test result
     */
    async testConnectivity() {
        try {
            const healthCheck = await textBeeClient.checkHealth();
            const serverInfo = await textBeeClient.getServerInfo();

            return {
                success: true,
                textbee_connected: healthCheck.connected,
                textbee_status: healthCheck.status,
                server_info: serverInfo.info,
                config_loaded: this.config !== null,
                queue_stats: await smsQueue.getQueueStats()
            };

        } catch (error) {
            console.error('Error testing SMS connectivity:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create singleton instance
const smsService = new SMSService();

module.exports = smsService;
