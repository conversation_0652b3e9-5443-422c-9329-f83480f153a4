<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - QRSAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>SMS Management</span>
                    </h6>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#settings-tab" data-bs-toggle="tab">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#templates-tab" data-bs-toggle="tab">
                                <i class="fas fa-file-text"></i> Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#statistics-tab" data-bs-toggle="tab">
                                <i class="fas fa-chart-bar"></i> Statistics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#connectivity-tab" data-bs-toggle="tab">
                                <i class="fas fa-wifi"></i> Connectivity
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><%= title %></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testConnection()">
                            <i class="fas fa-wifi"></i> Test Connection
                        </button>
                    </div>
                </div>

                <!-- Alert container -->
                <div id="alert-container"></div>

                <!-- Tab content -->
                <div class="tab-content">
                    <!-- Settings Tab -->
                    <div class="tab-pane fade show active" id="settings-tab">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">SMS Configuration Settings</h5>
                            </div>
                            <div class="card-body">
                                <form id="settings-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="textbee_server_url" class="form-label">TextBee Server URL</label>
                                                <input type="url" class="form-control" id="textbee_server_url" name="textbee_server_url" 
                                                       value="<%= config?.textbee_server_url || '' %>" required>
                                                <div class="form-text">URL of your TextBee SMS gateway server</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="textbee_api_key" class="form-label">API Key (Optional)</label>
                                                <input type="password" class="form-control" id="textbee_api_key" name="textbee_api_key" 
                                                       value="<%= config?.textbee_api_key || '' %>">
                                                <div class="form-text">API key for authentication (if required)</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="default_sender_name" class="form-label">Default Sender Name</label>
                                                <input type="text" class="form-control" id="default_sender_name" name="default_sender_name" 
                                                       value="<%= config?.default_sender_name || 'QRSAMS' %>" maxlength="20" required>
                                                <div class="form-text">Name that appears as SMS sender (max 20 chars)</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_retry_attempts" class="form-label">Max Retry Attempts</label>
                                                <input type="number" class="form-control" id="max_retry_attempts" name="max_retry_attempts" 
                                                       value="<%= config?.max_retry_attempts || 3 %>" min="1" max="10" required>
                                                <div class="form-text">Maximum number of retry attempts for failed messages</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="retry_delay_minutes" class="form-label">Retry Delay (Minutes)</label>
                                                <input type="number" class="form-control" id="retry_delay_minutes" name="retry_delay_minutes" 
                                                       value="<%= config?.retry_delay_minutes || 5 %>" min="1" max="60" required>
                                                <div class="form-text">Delay between retry attempts</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="queue_batch_size" class="form-label">Queue Batch Size</label>
                                                <input type="number" class="form-control" id="queue_batch_size" name="queue_batch_size" 
                                                       value="<%= config?.queue_batch_size || 10 %>" min="1" max="100" required>
                                                <div class="form-text">Number of messages to process in each batch</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="enable_sms" name="enable_sms" 
                                                       <%= config?.enable_sms ? 'checked' : '' %>>
                                                <label class="form-check-label" for="enable_sms">
                                                    Enable SMS Notifications
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="enable_delivery_reports" name="enable_delivery_reports" 
                                                       <%= config?.enable_delivery_reports ? 'checked' : '' %>>
                                                <label class="form-check-label" for="enable_delivery_reports">
                                                    Enable Delivery Reports
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Tab -->
                    <div class="tab-pane fade" id="templates-tab">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">SMS Message Templates</h5>
                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#templateModal">
                                    <i class="fas fa-plus"></i> Add Template
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Type</th>
                                                <th>Template</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="templates-table">
                                            <% if (templates && templates.length > 0) { %>
                                                <% templates.forEach(template => { %>
                                                    <tr>
                                                        <td><%= template.template_name %></td>
                                                        <td><span class="badge bg-secondary"><%= template.template_type %></span></td>
                                                        <td class="text-truncate" style="max-width: 300px;"><%= template.message_template %></td>
                                                        <td>
                                                            <% if (template.is_active) { %>
                                                                <span class="badge bg-success">Active</span>
                                                            <% } else { %>
                                                                <span class="badge bg-secondary">Inactive</span>
                                                            <% } %>
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-sm btn-outline-primary" onclick="editTemplate(<%= template.template_id %>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteTemplate(<%= template.template_id %>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <% }); %>
                                            <% } else { %>
                                                <tr>
                                                    <td colspan="5" class="text-center text-muted">No templates found</td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Tab -->
                    <div class="tab-pane fade" id="statistics-tab">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Total Messages</h5>
                                        <h2 class="text-primary"><%= stats?.overall?.total_messages || 0 %></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Delivered</h5>
                                        <h2 class="text-success"><%= stats?.overall?.delivered_count || 0 %></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Failed</h5>
                                        <h2 class="text-danger"><%= stats?.overall?.failed_count || 0 %></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">Delivery Rate</h5>
                                        <h2 class="text-info"><%= stats?.deliveryRate || 0 %>%</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connectivity Tab -->
                    <div class="tab-pane fade" id="connectivity-tab">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">TextBee Connection Status</h5>
                            </div>
                            <div class="card-body">
                                <div id="connectivity-status">
                                    <% if (connectivity?.textbee_connected) { %>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle"></i> Connected to TextBee server
                                        </div>
                                    <% } else { %>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-times-circle"></i> Not connected to TextBee server
                                        </div>
                                    <% } %>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Queue Status</h6>
                                        <ul class="list-group">
                                            <li class="list-group-item d-flex justify-content-between">
                                                Pending: <span class="badge bg-warning"><%= connectivity?.queue_stats?.stats?.pending || 0 %></span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                Sent: <span class="badge bg-success"><%= connectivity?.queue_stats?.stats?.sent || 0 %></span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between">
                                                Failed: <span class="badge bg-danger"><%= connectivity?.queue_stats?.stats?.failed || 0 %></span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Template Modal -->
    <div class="modal fade" id="templateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">SMS Template</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="template-form">
                        <input type="hidden" id="template_id" name="template_id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template_name" class="form-label">Template Name</label>
                                    <input type="text" class="form-control" id="template_name" name="template_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template_type" class="form-label">Template Type</label>
                                    <select class="form-select" id="template_type" name="template_type" required>
                                        <option value="">Select type...</option>
                                        <option value="attendance">Attendance</option>
                                        <option value="absence">Absence</option>
                                        <option value="late">Late Arrival</option>
                                        <option value="announcement">Announcement</option>
                                        <option value="emergency">Emergency</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="message_template" class="form-label">Message Template</label>
                            <textarea class="form-control" id="message_template" name="message_template" rows="4" required></textarea>
                            <div class="form-text">Use {{variable_name}} for dynamic content. Available variables: student_name, subject, teacher_name, time, school_name</div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">Active</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveTemplate()">Save Template</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // JavaScript functions for SMS configuration management
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Settings form submission
        document.getElementById('settings-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch('/sms-config/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Settings saved successfully!', 'success');
                } else {
                    showAlert('Error: ' + result.error, 'danger');
                }
            } catch (error) {
                showAlert('Error saving settings: ' + error.message, 'danger');
            }
        });

        // Test connection function
        async function testConnection() {
            try {
                const response = await fetch('/sms-config/test-connection', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success && result.connected) {
                    showAlert('Connection successful!', 'success');
                } else {
                    showAlert('Connection failed: ' + (result.error || 'Unknown error'), 'danger');
                }
            } catch (error) {
                showAlert('Error testing connection: ' + error.message, 'danger');
            }
        }

        // Template management functions
        async function saveTemplate() {
            const form = document.getElementById('template-form');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            try {
                const response = await fetch('/sms-config/templates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Template saved successfully!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide();
                    location.reload(); // Refresh to show updated templates
                } else {
                    showAlert('Error: ' + result.error, 'danger');
                }
            } catch (error) {
                showAlert('Error saving template: ' + error.message, 'danger');
            }
        }

        function editTemplate(templateId) {
            // Implementation for editing templates
            showAlert('Edit template functionality to be implemented', 'info');
        }

        async function deleteTemplate(templateId) {
            if (!confirm('Are you sure you want to delete this template?')) {
                return;
            }
            
            try {
                const response = await fetch(`/sms-config/templates/${templateId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Template deleted successfully!', 'success');
                    location.reload(); // Refresh to show updated templates
                } else {
                    showAlert('Error: ' + result.error, 'danger');
                }
            } catch (error) {
                showAlert('Error deleting template: ' + error.message, 'danger');
            }
        }
    </script>
</body>
</html>
