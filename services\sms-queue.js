const dbConnection = require('../database/connection');
const textBeeClient = require('./textbee-client');
const moment = require('moment');

/**
 * SMS Queue Management System
 * Handles message queuing, retry logic, batch processing, and background job processing
 */
class SMSQueue {
    constructor() {
        this.isProcessing = false;
        this.processingInterval = null;
        this.processingIntervalMs = 30000; // 30 seconds
        this.batchSize = 10;
        this.maxRetries = 3;
    }

    /**
     * Initialize the SMS queue system
     */
    async initialize() {
        try {
            await this.loadConfig();
            await this.startBackgroundProcessing();
            console.log('SMS Queue system initialized successfully');
        } catch (error) {
            console.error('Failed to initialize SMS Queue:', error.message);
            throw error;
        }
    }

    /**
     * Load configuration from database
     */
    async loadConfig() {
        try {
            const config = await dbConnection.get('SELECT * FROM sms_config LIMIT 1');
            if (config) {
                this.batchSize = config.queue_batch_size || 10;
                this.maxRetries = config.max_retry_attempts || 3;
                this.processingIntervalMs = (config.retry_delay_minutes || 5) * 60 * 1000;
            }
        } catch (error) {
            console.error('Error loading SMS queue configuration:', error.message);
        }
    }

    /**
     * Add message to SMS queue
     * @param {Object} messageData - Message data to queue
     * @returns {Promise<Object>} - Queue result
     */
    async addToQueue(messageData) {
        try {
            const {
                student_id,
                parent_contact,
                message_content,
                template_id = null,
                priority = 5,
                teacher_id = null,
                session_id = null,
                attendance_id = null,
                scheduled_at = null
            } = messageData;

            // Validate required fields
            if (!student_id || !parent_contact || !message_content) {
                throw new Error('Missing required fields: student_id, parent_contact, message_content');
            }

            // Insert into queue
            const result = await dbConnection.run(
                `INSERT INTO sms_queue (
                    student_id, parent_contact, message_content, template_id, priority,
                    teacher_id, session_id, attendance_id, scheduled_at, max_retries
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    student_id, parent_contact, message_content, template_id, priority,
                    teacher_id, session_id, attendance_id, 
                    scheduled_at || new Date().toISOString(),
                    this.maxRetries
                ]
            );

            console.log(`Message queued successfully with ID: ${result.lastID}`);
            
            return {
                success: true,
                queueId: result.lastID,
                message: 'Message added to queue successfully'
            };

        } catch (error) {
            console.error('Error adding message to queue:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Add multiple messages to queue in batch
     * @param {Array} messages - Array of message objects
     * @returns {Promise<Object>} - Batch queue result
     */
    async addBatchToQueue(messages) {
        try {
            const results = [];
            let successCount = 0;
            let failureCount = 0;

            for (const message of messages) {
                const result = await this.addToQueue(message);
                results.push({
                    ...message,
                    queueResult: result
                });

                if (result.success) {
                    successCount++;
                } else {
                    failureCount++;
                }
            }

            return {
                success: successCount > 0,
                totalQueued: successCount,
                totalFailed: failureCount,
                results: results
            };

        } catch (error) {
            console.error('Error adding batch to queue:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Process pending messages in the queue
     * @returns {Promise<Object>} - Processing result
     */
    async processQueue() {
        if (this.isProcessing) {
            console.log('Queue processing already in progress, skipping...');
            return { success: false, message: 'Already processing' };
        }

        this.isProcessing = true;
        
        try {
            console.log('Starting SMS queue processing...');

            // Get pending messages ordered by priority and scheduled time
            const pendingMessages = await dbConnection.all(
                `SELECT * FROM sms_queue 
                 WHERE status = 'pending' 
                   AND scheduled_at <= ? 
                   AND retry_count < max_retries
                 ORDER BY priority ASC, scheduled_at ASC 
                 LIMIT ?`,
                [new Date().toISOString(), this.batchSize]
            );

            if (pendingMessages.length === 0) {
                console.log('No pending messages to process');
                return { success: true, processed: 0 };
            }

            console.log(`Processing ${pendingMessages.length} pending messages...`);

            let processedCount = 0;
            let successCount = 0;
            let failureCount = 0;

            for (const queueItem of pendingMessages) {
                try {
                    // Mark as processing
                    await this.updateQueueStatus(queueItem.queue_id, 'processing');

                    // Attempt to send SMS
                    const sendResult = await textBeeClient.sendSMS({
                        recipient: queueItem.parent_contact,
                        message: queueItem.message_content,
                        priority: queueItem.priority
                    });

                    if (sendResult.success) {
                        // Mark as sent and log success
                        await this.updateQueueStatus(queueItem.queue_id, 'sent', {
                            textbee_message_id: sendResult.messageId,
                            processed_at: new Date().toISOString()
                        });

                        await this.logSMSResult(queueItem, sendResult, 'sent');
                        successCount++;

                    } else {
                        // Handle failure
                        await this.handleFailedMessage(queueItem, sendResult);
                        failureCount++;
                    }

                    processedCount++;

                } catch (error) {
                    console.error(`Error processing queue item ${queueItem.queue_id}:`, error.message);
                    await this.handleFailedMessage(queueItem, { error: error.message });
                    failureCount++;
                    processedCount++;
                }
            }

            console.log(`Queue processing completed. Processed: ${processedCount}, Success: ${successCount}, Failed: ${failureCount}`);

            return {
                success: true,
                processed: processedCount,
                successful: successCount,
                failed: failureCount
            };

        } catch (error) {
            console.error('Error processing SMS queue:', error.message);
            return {
                success: false,
                error: error.message
            };
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Handle failed message processing
     * @param {Object} queueItem - Queue item that failed
     * @param {Object} sendResult - Send result with error information
     */
    async handleFailedMessage(queueItem, sendResult) {
        try {
            const newRetryCount = queueItem.retry_count + 1;
            const shouldRetry = sendResult.shouldRetry && newRetryCount < queueItem.max_retries;

            if (shouldRetry) {
                // Schedule for retry with exponential backoff
                const retryDelay = Math.pow(2, newRetryCount) * 5; // 5, 10, 20 minutes
                const nextRetryTime = moment().add(retryDelay, 'minutes').toISOString();

                await dbConnection.run(
                    `UPDATE sms_queue 
                     SET status = 'pending', retry_count = ?, scheduled_at = ?, error_message = ?
                     WHERE queue_id = ?`,
                    [newRetryCount, nextRetryTime, sendResult.error, queueItem.queue_id]
                );

                console.log(`Message ${queueItem.queue_id} scheduled for retry ${newRetryCount} at ${nextRetryTime}`);

            } else {
                // Mark as permanently failed
                await this.updateQueueStatus(queueItem.queue_id, 'failed', {
                    error_message: sendResult.error,
                    processed_at: new Date().toISOString()
                });

                console.log(`Message ${queueItem.queue_id} marked as permanently failed after ${newRetryCount} attempts`);
            }

            // Log the failure
            await this.logSMSResult(queueItem, sendResult, shouldRetry ? 'pending' : 'failed');

        } catch (error) {
            console.error('Error handling failed message:', error.message);
        }
    }

    /**
     * Update queue item status
     * @param {number} queueId - Queue item ID
     * @param {string} status - New status
     * @param {Object} additionalData - Additional data to update
     */
    async updateQueueStatus(queueId, status, additionalData = {}) {
        try {
            const updateFields = ['status = ?'];
            const updateValues = [status];

            // Add additional fields to update
            for (const [key, value] of Object.entries(additionalData)) {
                updateFields.push(`${key} = ?`);
                updateValues.push(value);
            }

            updateValues.push(queueId);

            await dbConnection.run(
                `UPDATE sms_queue SET ${updateFields.join(', ')} WHERE queue_id = ?`,
                updateValues
            );

        } catch (error) {
            console.error('Error updating queue status:', error.message);
        }
    }

    /**
     * Log SMS result to sms_logs table
     * @param {Object} queueItem - Original queue item
     * @param {Object} sendResult - Send result
     * @param {string} status - Final status
     */
    async logSMSResult(queueItem, sendResult, status) {
        try {
            await dbConnection.run(
                `INSERT INTO sms_logs (
                    queue_id, student_id, parent_contact, message_content, template_id,
                    delivery_status, teacher_id, session_id, attendance_id,
                    textbee_message_id, textbee_response, error_message, retry_count
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    queueItem.queue_id,
                    queueItem.student_id,
                    queueItem.parent_contact,
                    queueItem.message_content,
                    queueItem.template_id,
                    status,
                    queueItem.teacher_id,
                    queueItem.session_id,
                    queueItem.attendance_id,
                    sendResult.messageId || null,
                    JSON.stringify(sendResult.response || {}),
                    sendResult.error || null,
                    queueItem.retry_count
                ]
            );

        } catch (error) {
            console.error('Error logging SMS result:', error.message);
        }
    }

    /**
     * Start background processing of the queue
     */
    async startBackgroundProcessing() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
        }

        console.log(`Starting SMS queue background processing (interval: ${this.processingIntervalMs}ms)`);

        this.processingInterval = setInterval(async () => {
            try {
                await this.processQueue();
            } catch (error) {
                console.error('Error in background queue processing:', error.message);
            }
        }, this.processingIntervalMs);

        // Process immediately on start
        setTimeout(() => this.processQueue(), 5000);
    }

    /**
     * Stop background processing
     */
    stopBackgroundProcessing() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
            console.log('SMS queue background processing stopped');
        }
    }

    /**
     * Get queue statistics
     * @returns {Promise<Object>} - Queue statistics
     */
    async getQueueStats() {
        try {
            const stats = await dbConnection.get(
                `SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                 FROM sms_queue`
            );

            return {
                success: true,
                stats: stats
            };

        } catch (error) {
            console.error('Error getting queue stats:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cancel a queued message
     * @param {number} queueId - Queue item ID
     * @returns {Promise<Object>} - Cancel result
     */
    async cancelMessage(queueId) {
        try {
            const result = await dbConnection.run(
                'UPDATE sms_queue SET status = ?, processed_at = ? WHERE queue_id = ? AND status = ?',
                ['cancelled', new Date().toISOString(), queueId, 'pending']
            );

            if (result.changes > 0) {
                return {
                    success: true,
                    message: 'Message cancelled successfully'
                };
            } else {
                return {
                    success: false,
                    error: 'Message not found or already processed'
                };
            }

        } catch (error) {
            console.error('Error cancelling message:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cleanup old processed messages
     * @param {number} daysOld - Days old to cleanup (default: 30)
     * @returns {Promise<Object>} - Cleanup result
     */
    async cleanupOldMessages(daysOld = 30) {
        try {
            const cutoffDate = moment().subtract(daysOld, 'days').toISOString();
            
            const result = await dbConnection.run(
                `DELETE FROM sms_queue 
                 WHERE status IN ('sent', 'failed', 'cancelled') 
                   AND processed_at < ?`,
                [cutoffDate]
            );

            return {
                success: true,
                deletedCount: result.changes,
                message: `Cleaned up ${result.changes} old messages`
            };

        } catch (error) {
            console.error('Error cleaning up old messages:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create singleton instance
const smsQueue = new SMSQueue();

module.exports = smsQueue;
