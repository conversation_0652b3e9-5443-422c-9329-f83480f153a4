<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - QR Attendance System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <%- include('../layout', { user: user }) %>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-qr-code me-2"></i>Test QR Codes</h2>
                    <a href="/attendance/scanner" class="btn btn-primary">
                        <i class="bi bi-qr-code-scan me-2"></i>Go to Scanner
                    </a>
                </div>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Testing Instructions:</strong>
                    <ol class="mb-0 mt-2">
                        <li>Open the QR Scanner in another tab/window</li>
                        <li>Select an active session</li>
                        <li>Point your camera at any student QR code below</li>
                        <li>Watch the attendance get recorded in real-time</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Student QR Codes -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-people me-2"></i>Student QR Codes
                        </h5>
                    </div>
                    <div class="card-body">
                        <% if (students.length === 0) { %>
                            <div class="text-center text-muted">
                                <i class="bi bi-people display-4"></i>
                                <p class="mt-2">No students found. Please add some students first.</p>
                                <a href="/students/new" class="btn btn-primary">Add Students</a>
                            </div>
                        <% } else { %>
                            <div class="row">
                                <% students.forEach(student => { %>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border">
                                            <div class="card-body text-center">
                                                <h6 class="card-title"><%= student.getFullName() %></h6>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        LRN: <%= student.lrn %><br>
                                                        <%= student.grade_level %> - <%= student.section %>
                                                    </small>
                                                </p>
                                                <div id="qr-student-<%= student.student_id %>" class="qr-container mb-2">
                                                    <!-- QR code will be generated here -->
                                                </div>
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="generateStudentQR(<%= student.student_id %>, '<%= student.qr_code_data %>')">
                                                    Generate QR
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
            
            <!-- Session QR Codes -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-event me-2"></i>Session QR Codes
                        </h5>
                    </div>
                    <div class="card-body">
                        <% if (sessions.length === 0) { %>
                            <div class="text-center text-muted">
                                <i class="bi bi-calendar-x display-4"></i>
                                <p class="mt-2">No active sessions found. Please create a session first.</p>
                                <a href="/sessions/new" class="btn btn-primary">Create Session</a>
                            </div>
                        <% } else { %>
                            <div class="row">
                                <% sessions.forEach(session => { %>
                                    <div class="col-12 mb-3">
                                        <div class="card border">
                                            <div class="card-body">
                                                <div class="row align-items-center">
                                                    <div class="col-md-8">
                                                        <h6 class="card-title"><%= session.subject_name %></h6>
                                                        <p class="card-text">
                                                            <small class="text-muted">
                                                                Room: <%= session.room_number %><br>
                                                                <%= new Date(session.date_time).toLocaleString() %><br>
                                                                Status: <span class="badge bg-<%= session.status === 'active' ? 'success' : 'secondary' %>">
                                                                    <%= session.status.toUpperCase() %>
                                                                </span>
                                                            </small>
                                                        </p>
                                                    </div>
                                                    <div class="col-md-4 text-center">
                                                        <div id="qr-session-<%= session.session_id %>" class="qr-container mb-2">
                                                            <!-- QR code will be generated here -->
                                                        </div>
                                                        <button class="btn btn-sm btn-outline-success" 
                                                                onclick="generateSessionQR(<%= session.session_id %>)">
                                                            Generate QR
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-question-circle me-2"></i>How to Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>For Desktop Testing:</h6>
                                <ol>
                                    <li>Generate student QR codes above</li>
                                    <li>Open the <a href="/attendance/scanner" target="_blank">QR Scanner</a> in a new tab</li>
                                    <li>Select an active session</li>
                                    <li>Use your phone to scan the QR codes on this page</li>
                                    <li>Watch attendance get recorded in real-time</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6>For Mobile Testing:</h6>
                                <ol>
                                    <li>Print or display student QR codes on another device</li>
                                    <li>Open the <a href="/attendance/scanner">QR Scanner</a></li>
                                    <li>Select an active session</li>
                                    <li>Point your camera at the QR codes</li>
                                    <li>See immediate feedback and attendance updates</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Student QR codes contain student information, while session QR codes are used to validate the scanning session. 
                            In normal operation, students scan their individual QR codes during an active session.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- QR Code Generation Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    
    <script>
        async function generateStudentQR(studentId, qrData) {
            try {
                const container = document.getElementById(`qr-student-${studentId}`);
                container.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                
                const canvas = document.createElement('canvas');
                await QRCode.toCanvas(canvas, qrData, {
                    width: 150,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
                
                container.innerHTML = '';
                container.appendChild(canvas);
            } catch (error) {
                console.error('Error generating student QR:', error);
                document.getElementById(`qr-student-${studentId}`).innerHTML = 
                    '<small class="text-danger">Error generating QR</small>';
            }
        }
        
        async function generateSessionQR(sessionId) {
            try {
                const container = document.getElementById(`qr-session-${sessionId}`);
                container.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                
                // Get session QR data from server
                const response = await fetch(`/sessions/${sessionId}/qr`);
                const data = await response.json();
                
                if (data.success) {
                    const canvas = document.createElement('canvas');
                    await QRCode.toCanvas(canvas, data.qr_data, {
                        width: 150,
                        margin: 2,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        }
                    });
                    
                    container.innerHTML = '';
                    container.appendChild(canvas);
                } else {
                    throw new Error(data.message || 'Failed to get session QR data');
                }
            } catch (error) {
                console.error('Error generating session QR:', error);
                document.getElementById(`qr-session-${sessionId}`).innerHTML = 
                    '<small class="text-danger">Error generating QR</small>';
            }
        }
        
        // Auto-generate QR codes for first few items
        document.addEventListener('DOMContentLoaded', () => {
            // Generate first 2 student QR codes automatically
            <% if (students.length > 0) { %>
                <% students.slice(0, 2).forEach(student => { %>
                    generateStudentQR(<%= student.student_id %>, '<%= student.qr_code_data %>');
                <% }); %>
            <% } %>
            
            // Generate first session QR code automatically
            <% if (sessions.length > 0) { %>
                generateSessionQR(<%= sessions[0].session_id %>);
            <% } %>
        });
    </script>
</body>
</html>
