<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || 'QRSAMS' %></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/public/css/style.css" rel="stylesheet">
    <link href="/public/css/students.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 0.5rem;
            margin: 0.2rem 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        .alert {
            border: none;
            border-radius: 0.75rem;
        }
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .badge {
            font-size: 0.75em;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
        }
        .quick-action-card {
            transition: transform 0.2s ease;
            cursor: pointer;
        }
        .quick-action-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <% if (typeof user !== 'undefined' && user) { %>
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            <h4 class="text-white">QRSAMS</h4>
                            <small class="text-white-50">QR Student Attendance</small>
                        </div>
                        
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard">
                                    <i class="bi bi-speedometer2 me-2"></i>
                                    Dashboard
                                </a>
                            </li>
                            
                            <% if (user.role === 'admin') { %>
                                <li class="nav-item">
                                    <a class="nav-link" href="/users">
                                        <i class="bi bi-people me-2"></i>
                                        User Management
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/register">
                                        <i class="bi bi-person-plus me-2"></i>
                                        Add User
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/users/import/csv">
                                        <i class="bi bi-upload me-2"></i>
                                        Import Users
                                    </a>
                                </li>
                            <% } %>
                            
                            <% if (user.role === 'admin' || user.role === 'principal') { %>
                                <li class="nav-item">
                                    <a class="nav-link" href="/students">
                                        <i class="bi bi-mortarboard me-2"></i>
                                        Students
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/subjects">
                                        <i class="bi bi-book me-2"></i>
                                        Subjects
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/reports">
                                        <i class="bi bi-graph-up me-2"></i>
                                        Reports
                                    </a>
                                </li>
                            <% } %>

                            <!-- Session Management (All authenticated users) -->
                            <li class="nav-item">
                                <a class="nav-link" href="/sessions">
                                    <i class="bi bi-calendar-event me-2"></i>
                                    Sessions
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/sessions/dashboard">
                                    <i class="bi bi-speedometer me-2"></i>
                                    Active Sessions
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/schedule">
                                    <i class="bi bi-calendar-week me-2"></i>
                                    Schedule
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/attendance/scanner">
                                    <i class="bi bi-qr-code-scan me-2"></i>
                                    QR Scanner
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/attendance">
                                    <i class="bi bi-check-square me-2"></i>
                                    Attendance
                                </a>
                            </li>
                            <% if (user.role === 'admin' || user.role === 'teacher') { %>
                                <li class="nav-item">
                                    <a class="nav-link" href="/attendance/test-qr">
                                        <i class="bi bi-qr-code me-2"></i>
                                        Test QR Codes
                                    </a>
                                </li>
                            <% } %>

                            
                            <hr class="text-white-50">
                            
                            <li class="nav-item">
                                <a class="nav-link" href="/profile">
                                    <i class="bi bi-person me-2"></i>
                                    My Profile
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                    <!-- Top navbar -->
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><%= title || 'Dashboard' %></h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                                    <div class="user-avatar me-2">
                                        <%= user.full_name.charAt(0).toUpperCase() %>
                                    </div>
                                    <%= user.full_name %>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>My Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="/logout" method="POST" class="d-inline">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Flash messages -->
                    <% if (typeof error !== 'undefined' && error) { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    
                    <% if (typeof success !== 'undefined' && success) { %>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>

                    <!-- Page content -->
                    <%- body %>
                </main>
            <% } else { %>
                <!-- Full width content for login/register pages -->
                <div class="col-12">
                    <!-- Flash messages -->
                    <% if (typeof error !== 'undefined' && error) { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    
                    <% if (typeof success !== 'undefined' && success) { %>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>

                    <%- body %>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/public/js/app.js"></script>
    <script src="/public/js/students.js"></script>
</body>
</html>
